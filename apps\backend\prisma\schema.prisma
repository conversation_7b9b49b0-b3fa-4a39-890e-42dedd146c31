generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model Organization {
  id          String   @id @default(cuid())
  name        String
  slug        String   @unique
  domain      String?
  settings    Json     @default("{}")
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  users       User[]
  agents      Agent[]
  tools       Tool[]
  workflows   Workflow[]
  sessions    Session[]
  providers   ProviderConfig[]
  knowledge   KnowledgeBase[]
  apiKeys     ApiKey[]
  providerUsage ProviderUsage[]

  @@map("organizations")
}

model User {
  id             String   @id @default(cuid())
  email          String   @unique
  passwordHash   String
  firstName      String
  lastName       String
  role           UserRole @default(DEVELOPER)
  isActive       Boolean  @default(true)
  lastLoginAt    DateTime?
  createdAt      DateTime @default(now())
  updatedAt      DateTime @updatedAt

  organizationId String
  organization   Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)

  sessions       Session[]
  apiKeys        ApiKey[]
  auditLogs      AuditLog[]

  @@map("users")
}

model ApiKey {
  id          String   @id @default(cuid())
  name        String
  keyHash     String   @unique
  permissions Json     @default("[]")
  isActive    Boolean  @default(true)
  lastUsedAt  DateTime?
  expiresAt   DateTime?
  createdAt   DateTime @default(now())

  organizationId String
  organization   Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)
  
  userId      String
  user        User    @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("api_keys")
}

model Agent {
  id           String    @id @default(cuid())
  name         String
  description  String?
  type         AgentType @default(STANDALONE)
  systemPrompt String
  model        String
  provider     String
  temperature  Float     @default(0.7)
  maxTokens    Int       @default(2048)
  isActive     Boolean   @default(true)
  skills       Json      @default("[]")
  metadata     Json      @default("{}")
  createdAt    DateTime  @default(now())
  updatedAt    DateTime  @updatedAt

  organizationId String
  organization   Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)

  sessions       Session[]
  workflowNodes  WorkflowNode[]
  toolConnections AgentTool[]

  @@map("agents")
}

model Tool {
  id          String     @id @default(cuid())
  name        String
  description String?
  type        ToolType   @default(FUNCTION_CALL)
  config      Json       @default("{}")
  inputSchema Json
  outputSchema Json?
  isActive    Boolean    @default(true)
  metadata    Json       @default("{}")
  createdAt   DateTime   @default(now())
  updatedAt   DateTime   @updatedAt

  organizationId String
  organization   Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)

  executions     ToolExecution[]
  workflowNodes  WorkflowNode[]
  agentConnections AgentTool[]

  @@map("tools")
}

model AgentTool {
  id       String @id @default(cuid())
  agentId  String
  toolId   String
  config   Json   @default("{}")
  
  agent    Agent  @relation(fields: [agentId], references: [id], onDelete: Cascade)
  tool     Tool   @relation(fields: [toolId], references: [id], onDelete: Cascade)

  @@unique([agentId, toolId])
  @@map("agent_tools")
}

model Workflow {
  id          String         @id @default(cuid())
  name        String
  description String?
  definition  Json
  isActive    Boolean        @default(true)
  version     Int            @default(1)
  metadata    Json           @default("{}")
  createdAt   DateTime       @default(now())
  updatedAt   DateTime       @updatedAt

  organizationId String
  organization   Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)

  nodes       WorkflowNode[]
  executions  WorkflowExecution[]

  @@map("workflows")
}

model WorkflowNode {
  id         String       @id @default(cuid())
  type       NodeType
  position   Json
  config     Json         @default("{}")
  
  workflowId String
  workflow   Workflow     @relation(fields: [workflowId], references: [id], onDelete: Cascade)
  
  agentId    String?
  agent      Agent?       @relation(fields: [agentId], references: [id])
  
  toolId     String?
  tool       Tool?        @relation(fields: [toolId], references: [id])

  @@map("workflow_nodes")
}

model Session {
  id          String        @id @default(cuid())
  name        String?
  context     Json          @default("{}")
  memory      Json          @default("[]")
  status      SessionStatus @default(ACTIVE)
  metadata    Json          @default("{}")
  createdAt   DateTime      @default(now())
  updatedAt   DateTime      @updatedAt
  expiresAt   DateTime?

  organizationId String
  organization   Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)

  userId      String?
  user        User?    @relation(fields: [userId], references: [id])

  agentId     String?
  agent       Agent?   @relation(fields: [agentId], references: [id])

  messages    Message[]
  executions  ToolExecution[]
  workflowExecutions WorkflowExecution[]

  @@map("sessions")
}

model Message {
  id        String      @id @default(cuid())
  role      MessageRole
  content   String
  metadata  Json        @default("{}")
  createdAt DateTime    @default(now())

  sessionId String
  session   Session @relation(fields: [sessionId], references: [id], onDelete: Cascade)

  @@map("messages")
}

model ToolExecution {
  id          String          @id @default(cuid())
  input       Json
  output      Json?
  status      ExecutionStatus @default(PENDING)
  error       String?
  duration    Int?
  metadata    Json            @default("{}")
  createdAt   DateTime        @default(now())
  completedAt DateTime?

  toolId    String
  tool      Tool    @relation(fields: [toolId], references: [id])

  sessionId String?
  session   Session? @relation(fields: [sessionId], references: [id])

  workflowExecutionId String?
  workflowExecution   WorkflowExecution? @relation(fields: [workflowExecutionId], references: [id])

  @@map("tool_executions")
}

model WorkflowExecution {
  id          String          @id @default(cuid())
  input       Json
  output      Json?
  status      ExecutionStatus @default(PENDING)
  error       String?
  metadata    Json            @default("{}")
  createdAt   DateTime        @default(now())
  completedAt DateTime?

  workflowId String
  workflow   Workflow @relation(fields: [workflowId], references: [id])

  sessionId String?
  session   Session? @relation(fields: [sessionId], references: [id])

  toolExecutions ToolExecution[]

  @@map("workflow_executions")
}

model ProviderConfig {
  id          String   @id @default(cuid())
  provider    String
  model       String
  config      Json     @default("{}")
  isActive    Boolean  @default(true)
  priority    Int      @default(0)
  rateLimits  Json     @default("{}")
  quotas      Json     @default("{}")
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  organizationId String
  organization   Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)

  @@unique([organizationId, provider, model])
  @@map("provider_configs")
}

model ProviderUsage {
  id               String   @id @default(cuid())
  provider         String
  model            String
  promptTokens     Int      @default(0)
  completionTokens Int      @default(0)
  totalTokens      Int      @default(0)
  cost             Float    @default(0)
  createdAt        DateTime @default(now())

  organizationId String
  organization   Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)

  @@map("provider_usage")
}

model KnowledgeBase {
  id          String   @id @default(cuid())
  name        String
  description String?
  config      Json     @default("{}")
  metadata    Json     @default("{}")
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  organizationId String
  organization   Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)

  documents   Document[]

  @@map("knowledge_bases")
}

model Document {
  id          String   @id @default(cuid())
  name        String
  content     String
  contentType String
  embedding   Json?
  metadata    Json     @default("{}")
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  knowledgeBaseId String
  knowledgeBase   KnowledgeBase @relation(fields: [knowledgeBaseId], references: [id], onDelete: Cascade)

  chunks      DocumentChunk[]

  @@map("documents")
}

model DocumentChunk {
  id        String @id @default(cuid())
  content   String
  embedding Json?
  metadata  Json   @default("{}")
  position  Int

  documentId String
  document   Document @relation(fields: [documentId], references: [id], onDelete: Cascade)

  @@map("document_chunks")
}

model AuditLog {
  id        String   @id @default(cuid())
  action    String
  resource  String
  details   Json     @default("{}")
  ipAddress String?
  userAgent String?
  createdAt DateTime @default(now())

  userId String
  user   User   @relation(fields: [userId], references: [id])

  @@map("audit_logs")
}

enum UserRole {
  SUPER_ADMIN
  ORG_ADMIN
  DEVELOPER
  VIEWER
}

enum AgentType {
  STANDALONE
  TOOL_DRIVEN
  HYBRID
  MULTI_TASK
  MULTI_PROVIDER
}

enum ToolType {
  FUNCTION_CALL
  REST_API
  RAG_RETRIEVAL
  BROWSER_ACTION
  DATABASE_QUERY
  FILE_PROCESSOR
}

enum NodeType {
  AGENT
  TOOL
  CONDITION
  PARALLEL
  TRIGGER
  OUTPUT
}

enum SessionStatus {
  ACTIVE
  PAUSED
  COMPLETED
  EXPIRED
  ERROR
}

enum MessageRole {
  USER
  ASSISTANT
  SYSTEM
  TOOL
}

enum ExecutionStatus {
  PENDING
  RUNNING
  COMPLETED
  FAILED
  CANCELLED
}