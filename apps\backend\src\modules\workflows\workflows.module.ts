import { Modu<PERSON> } from '@nestjs/common';
import { WorkflowsService } from './workflows.service';
import { WorkflowsController } from './workflows.controller';
import { PrismaModule } from '../../common/prisma/prisma.module';
import { AgentsModule } from '../agents/agents.module';
import { ToolsModule } from '../tools/tools.module';
import { ApixModule } from '../apix/apix.module';

@Module({
  imports: [PrismaModule, AgentsModule, ToolsModule, ApixModule],
  controllers: [WorkflowsController],
  providers: [WorkflowsService],
  exports: [WorkflowsService],
})
export class WorkflowsModule {}