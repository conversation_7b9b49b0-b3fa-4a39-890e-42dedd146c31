import { Entity, PrimaryGeneratedColumn, Column, CreateDateColumn, UpdateDateColumn } from 'typeorm';
import { ToolCategory, ToolStatus, ToolParameter, ToolAuthentication } from '@synapseai/shared';

@Entity('tools')
export class Tool {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column()
  name: string;

  @Column('text')
  description: string;

  @Column({
    type: 'enum',
    enum: ToolCategory,
  })
  category: ToolCategory;

  @Column()
  version: string;

  @Column('jsonb')
  parameters: ToolParameter[];

  @Column('jsonb')
  authentication: ToolAuthentication;

  @Column({
    type: 'enum',
    enum: ToolStatus,
    default: ToolStatus.ACTIVE,
  })
  status: ToolStatus;

  @Column('uuid')
  organizationId: string;

  @Column({ default: false })
  isPublic: boolean;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
}