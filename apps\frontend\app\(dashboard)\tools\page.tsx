'use client'

import { useState } from 'react'
import { useQuery } from 'react-query'
import { Button } from '@/components/ui/button'
import { ToolCard } from '@/components/tools/tool-card'
import { CreateToolDialog } from '@/components/tools/create-tool-dialog'
import { LoadingSpinner } from '@/components/ui/loading-spinner'
import { api } from '@/lib/api'
import { Plus } from 'lucide-react'

export default function ToolsPage() {
  const [showCreateDialog, setShowCreateDialog] = useState(false)

  const { data: tools, isLoading, refetch } = useQuery('tools', () =>
    api.get('/tools').then(res => res.data)
  )

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <LoadingSpinner />
      </div>
    )
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Tools</h1>
          <p className="text-muted-foreground">
            Manage your tools and integrations
          </p>
        </div>
        <Button onClick={() => setShowCreateDialog(true)}>
          <Plus className="h-4 w-4 mr-2" />
          Create Tool
        </Button>
      </div>

      {tools?.length === 0 ? (
        <div className="text-center py-12">
          <h3 className="text-lg font-semibold mb-2">No tools yet</h3>
          <p className="text-muted-foreground mb-4">
            Create your first tool to extend your agents' capabilities
          </p>
          <Button onClick={() => setShowCreateDialog(true)}>
            <Plus className="h-4 w-4 mr-2" />
            Create Tool
          </Button>
        </div>
      ) : (
        <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
          {tools?.map((tool: any) => (
            <ToolCard key={tool.id} tool={tool} onUpdate={refetch} />
          ))}
        </div>
      )}

      <CreateToolDialog
        open={showCreateDialog}
        onOpenChange={setShowCreateDialog}
        onSuccess={() => {
          setShowCreateDialog(false)
          refetch()
        }}
      />
    </div>
  )
}