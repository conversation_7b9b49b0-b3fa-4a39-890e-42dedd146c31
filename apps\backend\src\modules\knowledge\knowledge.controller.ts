import { Controller, Get, Post, Body, UseGuards, Request } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';
import { KnowledgeService } from './knowledge.service';
import { AuthGuard } from '../../common/guards/auth.guard';

@ApiTags('Knowledge')
@ApiBearerAuth()
@UseGuards(AuthGuard)
@Controller('knowledge')
export class KnowledgeController {
  constructor(private readonly knowledgeService: KnowledgeService) {}

  @Get()
  @ApiOperation({ summary: 'Get all knowledge bases' })
  @ApiResponse({ status: 200, description: 'List of knowledge bases' })
  findAll(@Request() req) {
    return this.knowledgeService.findAll(req.user.organizationId);
  }

  @Post()
  @ApiOperation({ summary: 'Create knowledge base' })
  @ApiResponse({ status: 201, description: 'Knowledge base created' })
  create(@Body() data: any, @Request() req) {
    return this.knowledgeService.create(data, req.user.organizationId);
  }
}