import { Controller, Get, Post, Body, Param, Put, Delete, UseGuards } from '@nestjs/common';
import { ApiT<PERSON>s, ApiBearerAuth } from '@nestjs/swagger';
import { AgentsService } from './agents.service';
import { CreateAgentDto, UpdateAgentDto, ExecuteAgentDto } from './dto/agent.dto';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { CurrentUser } from '../auth/decorators/current-user.decorator';
import { User } from '../auth/entities/user.entity';

@ApiTags('agents')
@Controller('agents')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth()
export class AgentsController {
  constructor(private readonly agentsService: AgentsService) {}

  @Post()
  async create(@Body() createAgentDto: CreateAgentDto, @CurrentUser() user: User) {
    return this.agentsService.create(createAgentDto, user.organizationId, user.id);
  }

  @Get()
  async findAll(@CurrentUser() user: User) {
    return this.agentsService.findAll(user.organizationId);
  }

  @Get(':id')
  async findOne(@Param('id') id: string, @CurrentUser() user: User) {
    return this.agentsService.findOne(id, user.organizationId);
  }

  @Put(':id')
  async update(
    @Param('id') id: string,
    @Body() updateAgentDto: UpdateAgentDto,
    @CurrentUser() user: User,
  ) {
    return this.agentsService.update(id, updateAgentDto, user.organizationId);
  }

  @Delete(':id')
  async remove(@Param('id') id: string, @CurrentUser() user: User) {
    return this.agentsService.remove(id, user.organizationId);
  }

  @Post(':id/execute')
  async execute(
    @Param('id') id: string,
    @Body() executeAgentDto: ExecuteAgentDto,
    @CurrentUser() user: User,
  ) {
    return this.agentsService.execute(id, executeAgentDto, user.organizationId);
  }
}