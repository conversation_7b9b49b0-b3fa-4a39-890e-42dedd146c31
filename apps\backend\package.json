{"name": "@synapseai/backend", "version": "1.0.0", "description": "SynapseAI Backend - NestJS API", "scripts": {"build": "nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start": "nest start", "start:dev": "nest start --watch", "start:debug": "nest start --debug --watch", "start:prod": "node dist/main", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json"}, "dependencies": {"@nestjs/common": "^10.2.10", "@nestjs/core": "^10.2.10", "@nestjs/platform-express": "^10.2.10", "@nestjs/websockets": "^10.2.10", "@nestjs/platform-socket.io": "^10.2.10", "@nestjs/jwt": "^10.2.0", "@nestjs/passport": "^10.0.2", "@nestjs/swagger": "^7.1.17", "@nestjs/config": "^3.1.1", "@nestjs/throttler": "^5.0.1", "@prisma/client": "^5.7.1", "prisma": "^5.7.1", "passport": "^0.7.0", "passport-jwt": "^4.0.1", "passport-local": "^1.0.0", "bcryptjs": "^2.4.3", "class-validator": "^0.14.0", "class-transformer": "^0.5.1", "zod": "^3.22.4", "socket.io": "^4.7.4", "redis": "^4.6.11", "axios": "^1.6.2", "openai": "^4.20.1", "@anthropic-ai/sdk": "^0.9.1", "uuid": "^9.0.1", "multer": "^1.4.5-lts.1", "sharp": "^0.33.1", "pdf-parse": "^1.1.1", "mammoth": "^1.6.0", "reflect-metadata": "^0.1.13", "rxjs": "^7.8.1"}, "devDependencies": {"@nestjs/cli": "^10.2.1", "@nestjs/schematics": "^10.0.3", "@nestjs/testing": "^10.2.10", "@types/express": "^4.17.21", "@types/jest": "^29.5.8", "@types/node": "^20.10.0", "@types/supertest": "^2.0.16", "@types/bcryptjs": "^2.4.6", "@types/passport-jwt": "^3.0.13", "@types/passport-local": "^1.0.38", "@types/uuid": "^9.0.7", "@types/multer": "^1.4.11", "@typescript-eslint/eslint-plugin": "^6.13.1", "@typescript-eslint/parser": "^6.13.1", "eslint": "^8.54.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^5.0.1", "jest": "^29.7.0", "prettier": "^3.1.0", "source-map-support": "^0.5.21", "supertest": "^6.3.3", "ts-jest": "^29.1.1", "ts-loader": "^9.5.1", "ts-node": "^10.9.1", "tsconfig-paths": "^4.2.0", "typescript": "^5.3.2"}}