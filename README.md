# SynapseAI - Production AI Orchestration Platform

🧠 **A complete, production-ready modular AI orchestration platform inspired by n8n.io**

## 🚀 Features

### ✨ **Core Capabilities**
- **🤖 Multi-Provider AI Agents** - OpenAI, Anthropic, Groq, Mistral support
- **🔧 Modular Tool System** - Function calls, REST APIs, RAG retrieval
- **⚡ Visual Workflow Builder** - React Flow-based n8n-style editor
- **🔄 Real-time Orchestration** - WebSocket-powered APIX protocol
- **🏢 Multi-tenant Architecture** - Organization-based isolation
- **📊 Advanced Analytics** - Performance monitoring and insights

### 🎯 **Workflow System**
- **Drag & Drop Builder** - Intuitive visual workflow creation
- **Node Types**: Triggers, AI Agents, Tools, Conditions, Delays, Outputs
- **Real-time Execution** - Live execution monitoring and debugging
- **Template System** - Dynamic input/output mapping
- **Conditional Logic** - Branching workflows with conditions
- **Import/Export** - JSON-based workflow sharing

### 🛡️ **Enterprise Ready**
- **RBAC Authentication** - Role-based access control
- **API Security** - JWT tokens, rate limiting, CORS
- **Audit Logging** - Complete activity tracking
- **Performance Monitoring** - Execution metrics and optimization
- **Multi-tenant Isolation** - Secure data separation

## 🏗️ Architecture

```
├── apps/
│   ├── backend/          # NestJS API Server
│   │   ├── src/
│   │   │   ├── modules/
│   │   │   │   ├── agents/     # AI Agent management
│   │   │   │   ├── tools/      # Tool execution engine
│   │   │   │   ├── workflows/  # Workflow orchestration
│   │   │   │   ├── apix/       # Real-time WebSocket gateway
│   │   │   │   ├── providers/  # AI provider integrations
│   │   │   │   ├── analytics/  # Performance analytics
│   │   │   │   └── auth/       # Authentication & authorization
│   │   │   └── common/         # Shared utilities
│   │   └── prisma/             # Database schema & migrations
│   └── frontend/         # Next.js Web Application
│       ├── app/                # App Router pages
│       ├── components/         # React components
│       │   ├── workflows/      # Workflow builder components
│       │   ├── agents/         # Agent management UI
│       │   ├── tools/          # Tool configuration UI
│       │   └── ui/             # Reusable UI components
│       └── lib/                # Utilities and services
└── packages/             # Shared packages (future)
```

## 🚀 Quick Start

### Prerequisites
- Node.js 18+
- PostgreSQL 14+
- Redis 6+
- npm or yarn

### 1. Clone & Install
```bash
git clone <repository>
cd synapseai-platform
npm install
```

### 2. Database Setup
```bash
# Create PostgreSQL database
createdb synapseai

# Set up environment
cp apps/backend/.env.example apps/backend/.env
cp apps/frontend/.env.example apps/frontend/.env

# Configure your database URL in apps/backend/.env
DATABASE_URL="postgresql://username:password@localhost:5432/synapseai"
```

### 3. Initialize Database
```bash
cd apps/backend
npx prisma generate
npx prisma db push
```

### 4. Start Development
```bash
# Start both backend and frontend
npm run dev

# Or start individually
npm run dev:backend  # Backend on http://localhost:3001
npm run dev:frontend # Frontend on http://localhost:3000
```

### 5. First Login
1. Navigate to http://localhost:3000
2. Register a new account (creates organization)
3. Start building your AI workflows!

## 🔧 Configuration

### Environment Variables

**Backend** (`apps/backend/.env`):
```env
DATABASE_URL="postgresql://user:pass@localhost:5432/synapseai"
REDIS_URL="redis://localhost:6379"
JWT_SECRET="your-secret-key"

# AI Provider Keys
OPENAI_API_KEY="sk-..."
ANTHROPIC_API_KEY="sk-ant-..."
GROQ_API_KEY="gsk_..."
MISTRAL_API_KEY="..."
```

**Frontend** (`apps/frontend/.env`):
```env
NEXT_PUBLIC_API_URL=http://localhost:3001
NEXT_PUBLIC_WS_URL=ws://localhost:3001
```

## 📖 Usage Guide

### Creating Your First Workflow

1. **Navigate to Workflows** → Click "Create Workflow"
2. **Design Your Flow**:
   - Drag nodes from the left palette
   - Connect nodes by dragging between connection points
   - Configure each node's properties in the right panel
3. **Test & Deploy**:
   - Use "Test Run" to execute with sample data
   - Save your workflow when ready
   - Monitor executions in real-time

### Workflow Node Types

| Node Type | Description | Use Cases |
|-----------|-------------|-----------|
| 🔌 **Trigger** | Entry point for workflows | Webhooks, schedules, manual |
| 🤖 **AI Agent** | LLM interactions | Chat, content generation, analysis |
| 🔧 **Tool** | External integrations | APIs, databases, file processing |
| 🔀 **Condition** | Branching logic | If/else workflows |
| ⏱️ **Delay** | Timed pauses | Rate limiting, scheduling |
| 📤 **Output** | Data formatting | Response formatting |

### Agent Management

1. **Create Agents** → Configure with specific roles and models
2. **Provider Selection** → Choose from OpenAI, Anthropic, Groq, Mistral
3. **System Prompts** → Define behavior and personality
4. **Live Chat** → Test agents directly in the interface

### Tool Development

1. **Function Tools** → Custom JavaScript functions
2. **REST API Tools** → External service integrations
3. **RAG Tools** → Knowledge base retrieval
4. **Input/Output Schemas** → Zod-based validation

## 📊 Analytics & Monitoring

- **Real-time Dashboards** → Execution metrics and trends
- **Performance Insights** → Response times and success rates
- **Usage Analytics** → Agent/tool utilization
- **Audit Logs** → Complete activity tracking

## 🔒 Security Features

- **Multi-tenant Isolation** → Organization-based data separation
- **Role-based Access** → SUPER_ADMIN, ORG_ADMIN, DEVELOPER, VIEWER
- **API Authentication** → JWT tokens with refresh mechanism
- **Rate Limiting** → Request throttling and abuse prevention
- **Input Validation** → Zod schemas for all endpoints

## 🚀 Production Deployment

### Docker Deployment
```bash
# Build and run with Docker Compose
docker-compose up -d
```

### Manual Deployment
```bash
# Build applications
npm run build

# Start production servers
npm run start:backend
npm run start:frontend
```

### Environment Checklist
- [ ] Set strong JWT secrets
- [ ] Configure production database
- [ ] Set up Redis cluster
- [ ] Configure AI provider API keys
- [ ] Set CORS origins
- [ ] Enable HTTPS
- [ ] Configure monitoring

## 🛠️ Development

### Project Structure
- **Monorepo** → Apps and shared packages
- **TypeScript** → Type safety throughout
- **Prisma** → Database ORM and migrations
- **React Flow** → Visual workflow editor
- **Shadcn/UI** → Modern UI components
- **Real-time** → WebSocket event streaming

### Key Technologies
- **Backend**: NestJS, Prisma, Redis, Socket.io
- **Frontend**: Next.js 14, React Flow, Tailwind CSS
- **Database**: PostgreSQL with Prisma
- **Authentication**: JWT with role-based access
- **Real-time**: WebSocket with APIX protocol

### Adding New Features
1. **Backend** → Create NestJS modules with controllers/services
2. **Database** → Update Prisma schema and migrate
3. **Frontend** → Add pages and components
4. **Integration** → Connect via API calls and WebSocket events

## 📚 API Documentation

Interactive API documentation available at:
- **Development**: http://localhost:3001/api/docs
- **Production**: https://yourdomain.com/api/docs

## 🧪 Testing

```bash
# Run backend tests
npm run test:backend

# Run frontend tests
npm run test:frontend

# Run E2E tests
npm run test:e2e
```

## 🤝 Contributing

1. Fork the repository
2. Create feature branch (`git checkout -b feature/amazing-feature`)
3. Commit changes (`git commit -m 'Add amazing feature'`)
4. Push to branch (`git push origin feature/amazing-feature`)
5. Open Pull Request

## 📝 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🆘 Support

- **Documentation**: [Wiki](../../wiki)
- **Issues**: [GitHub Issues](../../issues)
- **Discussions**: [GitHub Discussions](../../discussions)

---

**Built with ❤️ for the AI automation community**