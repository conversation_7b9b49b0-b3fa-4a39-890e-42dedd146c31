'use client'

import { useParams } from 'next/navigation'
import { WorkflowBuilder } from '@/components/workflows/workflow-builder'
import { useRouter } from 'next/navigation'

export default function EditWorkflowPage() {
  const params = useParams()
  const router = useRouter()
  const workflowId = params.id as string

  const handleSave = (workflow: any) => {
    // Workflow saved successfully
    router.push('/workflows')
  }

  return (
    <div className="h-screen">
      <WorkflowBuilder workflowId={workflowId} onSave={handleSave} />
    </div>
  )
}