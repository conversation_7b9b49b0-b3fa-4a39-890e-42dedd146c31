import { Injectable, BadRequestException, Logger } from '@nestjs/common';
import { PrismaService } from '../../common/prisma/prisma.service';
import OpenAI from 'openai';
import Anthropic from '@anthropic-ai/sdk';
import axios from 'axios';

interface GenerateRequest {
  provider: string;
  model: string;
  messages: Array<{ role: string; content: string }>;
  temperature?: number;
  maxTokens?: number;
  stream?: boolean;
  organizationId?: string;
}

interface GenerateResponse {
  content: string;
  tokenUsage?: {
    promptTokens: number;
    completionTokens: number;
    totalTokens: number;
  };
  provider: string;
  model: string;
  latency: number;
  cost?: number;
}

interface ProviderHealth {
  provider: string;
  isHealthy: boolean;
  latency: number;
  lastChecked: Date;
  errorRate: number;
  quotaRemaining?: number;
}

@Injectable()
export class ProvidersService {
  private readonly logger = new Logger(ProvidersService.name);
  private openai: OpenAI;
  private anthropic: Anthropic;
  private providerHealth: Map<string, ProviderHealth> = new Map();
  private rateLimits: Map<string, { count: number; resetTime: number }> = new Map();

  constructor(private prisma: PrismaService) {
    this.initializeProviders();
    this.startHealthChecks();
  }

  private initializeProviders() {
    // OpenAI
    if (process.env.OPENAI_API_KEY) {
      this.openai = new OpenAI({
        apiKey: process.env.OPENAI_API_KEY,
      });
      this.logger.log('OpenAI provider initialized');
    } else {
      this.logger.warn('OpenAI API key not found');
    }

    // Anthropic
    if (process.env.ANTHROPIC_API_KEY) {
      this.anthropic = new Anthropic({
        apiKey: process.env.ANTHROPIC_API_KEY,
      });
      this.logger.log('Anthropic provider initialized');
    } else {
      this.logger.warn('Anthropic API key not found');
    }

    // Initialize health status
    this.initializeHealthStatus();
  }

  private initializeHealthStatus() {
    const providers = ['openai', 'anthropic', 'groq', 'mistral', 'gemini', 'deepseek', 'together', 'perplexity'];
    
    providers.forEach(provider => {
      this.providerHealth.set(provider, {
        provider,
        isHealthy: true,
        latency: 0,
        lastChecked: new Date(),
        errorRate: 0,
      });
    });
  }

  private startHealthChecks() {
    // Check provider health every 5 minutes
    setInterval(() => {
      this.checkAllProvidersHealth();
    }, 5 * 60 * 1000);
  }

  async generateResponse(request: GenerateRequest): Promise<GenerateResponse> {
    const startTime = Date.now();
    
    try {
      // Smart routing: find best available provider
      const selectedProvider = await this.selectBestProvider(request);
      
      this.logger.log(`Routing request to ${selectedProvider} for model ${request.model}`);
      
      const response = await this.executeProviderRequest(selectedProvider, request);
      const latency = Date.now() - startTime;
      
      // Update provider health metrics
      this.updateProviderMetrics(selectedProvider, true, latency);
      
      // Log usage for billing/analytics
      await this.logUsage(request.organizationId, selectedProvider, request.model, response.tokenUsage);
      
      return {
        ...response,
        latency,
        cost: this.calculateCost(selectedProvider, request.model, response.tokenUsage),
      };
      
    } catch (error) {
      const latency = Date.now() - startTime;
      this.updateProviderMetrics(request.provider, false, latency);
      
      // Try fallback provider
      if (!request.provider.includes('fallback')) {
        this.logger.warn(`Primary provider ${request.provider} failed, trying fallback`);
        const fallbackRequest = { ...request, provider: await this.getFallbackProvider(request.provider) };
        return this.generateResponse(fallbackRequest);
      }
      
      this.logger.error(`Provider ${request.provider} failed:`, error);
      throw new BadRequestException(`Failed to generate response: ${error.message}`);
    }
  }

  private async selectBestProvider(request: GenerateRequest): Promise<string> {
    if (request.provider && request.provider !== 'auto') {
      // Check if specified provider is healthy
      const health = this.providerHealth.get(request.provider);
      if (health?.isHealthy && !this.isRateLimited(request.provider)) {
        return request.provider;
      }
    }

    // Smart routing: select best provider based on model capabilities and health
    const compatibleProviders = this.getCompatibleProviders(request.model);
    const healthyProviders = compatibleProviders.filter(provider => {
      const health = this.providerHealth.get(provider);
      return health?.isHealthy && !this.isRateLimited(provider);
    });

    if (healthyProviders.length === 0) {
      throw new BadRequestException('No healthy providers available');
    }

    // Sort by latency and error rate
    return healthyProviders.sort((a, b) => {
      const healthA = this.providerHealth.get(a);
      const healthB = this.providerHealth.get(b);
      const scoreA = healthA.latency + (healthA.errorRate * 1000);
      const scoreB = healthB.latency + (healthB.errorRate * 1000);
      return scoreA - scoreB;
    })[0];
  }

  private getCompatibleProviders(model: string): string[] {
    const modelProviderMap = {
      // OpenAI Models
      'gpt-4': ['openai'],
      'gpt-4-turbo': ['openai'],
      'gpt-4-turbo-preview': ['openai'],
      'gpt-3.5-turbo': ['openai'],
      'gpt-3.5-turbo-16k': ['openai'],
      
      // Anthropic Models
      'claude-3-opus-20240229': ['anthropic'],
      'claude-3-sonnet-20240229': ['anthropic'],
      'claude-3-haiku-20240307': ['anthropic'],
      'claude-3-5-sonnet-20241022': ['anthropic'],
      
      // Groq Models
      'mixtral-8x7b-32768': ['groq'],
      'llama2-70b-4096': ['groq'],
      'llama-3.1-8b-instant': ['groq'],
      'llama-3.1-70b-versatile': ['groq'],
      
      // Mistral Models
      'mistral-large-latest': ['mistral'],
      'mistral-medium-latest': ['mistral'],
      'mistral-small-latest': ['mistral'],
      'open-mixtral-8x7b': ['mistral'],
      
      // Google Gemini
      'gemini-pro': ['gemini'],
      'gemini-pro-vision': ['gemini'],
      'gemini-1.5-pro': ['gemini'],
      
      // DeepSeek
      'deepseek-chat': ['deepseek'],
      'deepseek-coder': ['deepseek'],
      
      // Together AI
      'meta-llama/Llama-2-70b-chat-hf': ['together'],
      'mistralai/Mixtral-8x7B-Instruct-v0.1': ['together'],
      
      // Perplexity
      'llama-3.1-sonar-small-128k-online': ['perplexity'],
      'llama-3.1-sonar-large-128k-online': ['perplexity'],
    };

    return modelProviderMap[model] || [];
  }

  private async executeProviderRequest(provider: string, request: GenerateRequest): Promise<GenerateResponse> {
    switch (provider.toLowerCase()) {
      case 'openai':
        return await this.generateOpenAI(request.model, request.messages, request.temperature, request.maxTokens);
      
      case 'anthropic':
        return await this.generateAnthropic(request.model, request.messages, request.temperature, request.maxTokens);
      
      case 'groq':
        return await this.generateGroq(request.model, request.messages, request.temperature, request.maxTokens);
      
      case 'mistral':
        return await this.generateMistral(request.model, request.messages, request.temperature, request.maxTokens);
      
      case 'gemini':
        return await this.generateGemini(request.model, request.messages, request.temperature, request.maxTokens);
      
      case 'deepseek':
        return await this.generateDeepSeek(request.model, request.messages, request.temperature, request.maxTokens);
      
      case 'together':
        return await this.generateTogether(request.model, request.messages, request.temperature, request.maxTokens);
      
      case 'perplexity':
        return await this.generatePerplexity(request.model, request.messages, request.temperature, request.maxTokens);
      
      default:
        throw new BadRequestException(`Unsupported provider: ${provider}`);
    }
  }

  private async generateOpenAI(
    model: string,
    messages: Array<{ role: string; content: string }>,
    temperature: number = 0.7,
    maxTokens: number = 2048,
  ): Promise<GenerateResponse> {
    if (!this.openai) {
      throw new BadRequestException('OpenAI not configured');
    }

    const response = await this.openai.chat.completions.create({
      model,
      messages: messages.map(m => ({
        role: m.role as 'system' | 'user' | 'assistant',
        content: m.content,
      })),
      temperature,
      max_tokens: maxTokens,
    });

    return {
      content: response.choices[0]?.message?.content || '',
      tokenUsage: {
        promptTokens: response.usage?.prompt_tokens || 0,
        completionTokens: response.usage?.completion_tokens || 0,
        totalTokens: response.usage?.total_tokens || 0,
      },
      provider: 'openai',
      model,
      latency: 0, // Will be set by caller
    };
  }

  private async generateAnthropic(
    model: string,
    messages: Array<{ role: string; content: string }>,
    temperature: number = 0.7,
    maxTokens: number = 2048,
  ): Promise<GenerateResponse> {
    if (!this.anthropic) {
      throw new BadRequestException('Anthropic not configured');
    }

    const systemMessage = messages.find(m => m.role === 'system');
    const conversationMessages = messages.filter(m => m.role !== 'system');

    const response = await this.anthropic.messages.create({
      model,
      max_tokens: maxTokens,
      temperature,
      system: systemMessage?.content,
      messages: conversationMessages.map(m => ({
        role: m.role === 'user' ? 'user' : 'assistant',
        content: m.content,
      })) as any,
    });

    return {
      content: response.content[0]?.type === 'text' ? response.content[0].text : '',
      tokenUsage: {
        promptTokens: response.usage?.input_tokens || 0,
        completionTokens: response.usage?.output_tokens || 0,
        totalTokens: (response.usage?.input_tokens || 0) + (response.usage?.output_tokens || 0),
      },
      provider: 'anthropic',
      model,
      latency: 0,
    };
  }

  private async generateGroq(
    model: string,
    messages: Array<{ role: string; content: string }>,
    temperature: number = 0.7,
    maxTokens: number = 2048,
  ): Promise<GenerateResponse> {
    if (!process.env.GROQ_API_KEY) {
      throw new BadRequestException('Groq not configured');
    }

    const response = await axios.post(
      'https://api.groq.com/openai/v1/chat/completions',
      {
        model,
        messages,
        temperature,
        max_tokens: maxTokens,
      },
      {
        headers: {
          'Authorization': `Bearer ${process.env.GROQ_API_KEY}`,
          'Content-Type': 'application/json',
        },
      }
    );

    return {
      content: response.data.choices[0]?.message?.content || '',
      tokenUsage: {
        promptTokens: response.data.usage?.prompt_tokens || 0,
        completionTokens: response.data.usage?.completion_tokens || 0,
        totalTokens: response.data.usage?.total_tokens || 0,
      },
      provider: 'groq',
      model,
      latency: 0,
    };
  }

  private async generateMistral(
    model: string,
    messages: Array<{ role: string; content: string }>,
    temperature: number = 0.7,
    maxTokens: number = 2048,
  ): Promise<GenerateResponse> {
    if (!process.env.MISTRAL_API_KEY) {
      throw new BadRequestException('Mistral not configured');
    }

    const response = await axios.post(
      'https://api.mistral.ai/v1/chat/completions',
      {
        model,
        messages,
        temperature,
        max_tokens: maxTokens,
      },
      {
        headers: {
          'Authorization': `Bearer ${process.env.MISTRAL_API_KEY}`,
          'Content-Type': 'application/json',
        },
      }
    );

    return {
      content: response.data.choices[0]?.message?.content || '',
      tokenUsage: {
        promptTokens: response.data.usage?.prompt_tokens || 0,
        completionTokens: response.data.usage?.completion_tokens || 0,
        totalTokens: response.data.usage?.total_tokens || 0,
      },
      provider: 'mistral',
      model,
      latency: 0,
    };
  }

  private async generateGemini(
    model: string,
    messages: Array<{ role: string; content: string }>,
    temperature: number = 0.7,
    maxTokens: number = 2048,
  ): Promise<GenerateResponse> {
    if (!process.env.GOOGLE_API_KEY) {
      throw new BadRequestException('Google Gemini not configured');
    }

    // Convert messages to Gemini format
    const contents = messages.map(m => ({
      role: m.role === 'assistant' ? 'model' : 'user',
      parts: [{ text: m.content }],
    }));

    const response = await axios.post(
      `https://generativelanguage.googleapis.com/v1beta/models/${model}:generateContent?key=${process.env.GOOGLE_API_KEY}`,
      {
        contents,
        generationConfig: {
          temperature,
          maxOutputTokens: maxTokens,
        },
      },
      {
        headers: {
          'Content-Type': 'application/json',
        },
      }
    );

    return {
      content: response.data.candidates[0]?.content?.parts[0]?.text || '',
      tokenUsage: {
        promptTokens: response.data.usageMetadata?.promptTokenCount || 0,
        completionTokens: response.data.usageMetadata?.candidatesTokenCount || 0,
        totalTokens: response.data.usageMetadata?.totalTokenCount || 0,
      },
      provider: 'gemini',
      model,
      latency: 0,
    };
  }

  private async generateDeepSeek(
    model: string,
    messages: Array<{ role: string; content: string }>,
    temperature: number = 0.7,
    maxTokens: number = 2048,
  ): Promise<GenerateResponse> {
    if (!process.env.DEEPSEEK_API_KEY) {
      throw new BadRequestException('DeepSeek not configured');
    }

    const response = await axios.post(
      'https://api.deepseek.com/v1/chat/completions',
      {
        model,
        messages,
        temperature,
        max_tokens: maxTokens,
      },
      {
        headers: {
          'Authorization': `Bearer ${process.env.DEEPSEEK_API_KEY}`,
          'Content-Type': 'application/json',
        },
      }
    );

    return {
      content: response.data.choices[0]?.message?.content || '',
      tokenUsage: {
        promptTokens: response.data.usage?.prompt_tokens || 0,
        completionTokens: response.data.usage?.completion_tokens || 0,
        totalTokens: response.data.usage?.total_tokens || 0,
      },
      provider: 'deepseek',
      model,
      latency: 0,
    };
  }

  private async generateTogether(
    model: string,
    messages: Array<{ role: string; content: string }>,
    temperature: number = 0.7,
    maxTokens: number = 2048,
  ): Promise<GenerateResponse> {
    if (!process.env.TOGETHER_API_KEY) {
      throw new BadRequestException('Together AI not configured');
    }

    const response = await axios.post(
      'https://api.together.xyz/v1/chat/completions',
      {
        model,
        messages,
        temperature,
        max_tokens: maxTokens,
      },
      {
        headers: {
          'Authorization': `Bearer ${process.env.TOGETHER_API_KEY}`,
          'Content-Type': 'application/json',
        },
      }
    );

    return {
      content: response.data.choices[0]?.message?.content || '',
      tokenUsage: {
        promptTokens: response.data.usage?.prompt_tokens || 0,
        completionTokens: response.data.usage?.completion_tokens || 0,
        totalTokens: response.data.usage?.total_tokens || 0,
      },
      provider: 'together',
      model,
      latency: 0,
    };
  }

  private async generatePerplexity(
    model: string,
    messages: Array<{ role: string; content: string }>,
    temperature: number = 0.7,
    maxTokens: number = 2048,
  ): Promise<GenerateResponse> {
    if (!process.env.PERPLEXITY_API_KEY) {
      throw new BadRequestException('Perplexity not configured');
    }

    const response = await axios.post(
      'https://api.perplexity.ai/chat/completions',
      {
        model,
        messages,
        temperature,
        max_tokens: maxTokens,
      },
      {
        headers: {
          'Authorization': `Bearer ${process.env.PERPLEXITY_API_KEY}`,
          'Content-Type': 'application/json',
        },
      }
    );

    return {
      content: response.data.choices[0]?.message?.content || '',
      tokenUsage: {
        promptTokens: response.data.usage?.prompt_tokens || 0,
        completionTokens: response.data.usage?.completion_tokens || 0,
        totalTokens: response.data.usage?.total_tokens || 0,
      },
      provider: 'perplexity',
      model,
      latency: 0,
    };
  }

  private async getFallbackProvider(failedProvider: string): Promise<string> {
    const fallbackMap = {
      'openai': ['anthropic', 'groq', 'mistral'],
      'anthropic': ['openai', 'mistral', 'groq'],
      'groq': ['openai', 'anthropic', 'mistral'],
      'mistral': ['openai', 'anthropic', 'groq'],
      'gemini': ['openai', 'anthropic'],
      'deepseek': ['openai', 'groq'],
      'together': ['openai', 'groq'],
      'perplexity': ['openai', 'anthropic'],
    };

    const fallbacks = fallbackMap[failedProvider] || ['openai'];
    
    for (const fallback of fallbacks) {
      const health = this.providerHealth.get(fallback);
      if (health?.isHealthy && !this.isRateLimited(fallback)) {
        return fallback + '-fallback';
      }
    }

    throw new BadRequestException('No fallback providers available');
  }

  private isRateLimited(provider: string): boolean {
    const limit = this.rateLimits.get(provider);
    if (!limit) return false;
    
    if (Date.now() > limit.resetTime) {
      this.rateLimits.delete(provider);
      return false;
    }
    
    return limit.count >= this.getProviderRateLimit(provider);
  }

  private getProviderRateLimit(provider: string): number {
    const limits = {
      'openai': 60,      // 60 requests per minute
      'anthropic': 50,   // 50 requests per minute
      'groq': 100,       // 100 requests per minute
      'mistral': 60,     // 60 requests per minute
      'gemini': 60,      // 60 requests per minute
      'deepseek': 100,   // 100 requests per minute
      'together': 100,   // 100 requests per minute
      'perplexity': 60,  // 60 requests per minute
    };
    
    return limits[provider] || 60;
  }

  private updateProviderMetrics(provider: string, success: boolean, latency: number) {
    const health = this.providerHealth.get(provider);
    if (!health) return;

    // Update latency (rolling average)
    health.latency = (health.latency * 0.9) + (latency * 0.1);
    
    // Update error rate
    if (success) {
      health.errorRate = health.errorRate * 0.95;
    } else {
      health.errorRate = Math.min(1, health.errorRate + 0.1);
    }
    
    // Update health status
    health.isHealthy = health.errorRate < 0.5;
    health.lastChecked = new Date();
    
    // Update rate limiting
    const limit = this.rateLimits.get(provider) || { count: 0, resetTime: Date.now() + 60000 };
    if (Date.now() > limit.resetTime) {
      limit.count = 1;
      limit.resetTime = Date.now() + 60000;
    } else {
      limit.count++;
    }
    this.rateLimits.set(provider, limit);
  }

  private async checkAllProvidersHealth() {
    const providers = Array.from(this.providerHealth.keys());
    
    for (const provider of providers) {
      try {
        await this.checkProviderHealth(provider);
      } catch (error) {
        this.logger.error(`Health check failed for ${provider}:`, error);
        const health = this.providerHealth.get(provider);
        if (health) {
          health.isHealthy = false;
          health.errorRate = Math.min(1, health.errorRate + 0.2);
        }
      }
    }
  }

  private async checkProviderHealth(provider: string): Promise<void> {
    // Simple health check with a lightweight request
    const testMessage = [{ role: 'user', content: 'Hi' }];
    
    try {
      const startTime = Date.now();
      await this.executeProviderRequest(provider, {
        provider,
        model: this.getDefaultModel(provider),
        messages: testMessage,
        temperature: 0.1,
        maxTokens: 10,
      });
      
      const latency = Date.now() - startTime;
      this.updateProviderMetrics(provider, true, latency);
      
    } catch (error) {
      this.updateProviderMetrics(provider, false, 5000);
      throw error;
    }
  }

  private getDefaultModel(provider: string): string {
    const defaultModels = {
      'openai': 'gpt-3.5-turbo',
      'anthropic': 'claude-3-haiku-20240307',
      'groq': 'llama-3.1-8b-instant',
      'mistral': 'mistral-small-latest',
      'gemini': 'gemini-pro',
      'deepseek': 'deepseek-chat',
      'together': 'meta-llama/Llama-2-70b-chat-hf',
      'perplexity': 'llama-3.1-sonar-small-128k-online',
    };
    
    return defaultModels[provider] || 'gpt-3.5-turbo';
  }

  private calculateCost(provider: string, model: string, tokenUsage: any): number {
    // Cost per 1K tokens (simplified pricing)
    const pricing = {
      'openai': {
        'gpt-4': { input: 0.03, output: 0.06 },
        'gpt-4-turbo': { input: 0.01, output: 0.03 },
        'gpt-3.5-turbo': { input: 0.001, output: 0.002 },
      },
      'anthropic': {
        'claude-3-opus-20240229': { input: 0.015, output: 0.075 },
        'claude-3-sonnet-20240229': { input: 0.003, output: 0.015 },
        'claude-3-haiku-20240307': { input: 0.00025, output: 0.00125 },
      },
      'groq': {
        'mixtral-8x7b-32768': { input: 0.0002, output: 0.0002 },
        'llama2-70b-4096': { input: 0.0007, output: 0.0008 },
      },
      // Add more pricing as needed
    };

    const modelPricing = pricing[provider]?.[model];
    if (!modelPricing || !tokenUsage) return 0;

    const inputCost = (tokenUsage.promptTokens / 1000) * modelPricing.input;
    const outputCost = (tokenUsage.completionTokens / 1000) * modelPricing.output;
    
    return inputCost + outputCost;
  }

  private async logUsage(organizationId: string, provider: string, model: string, tokenUsage: any) {
    if (!organizationId || !tokenUsage) return;

    try {
      await this.prisma.providerUsage.create({
        data: {
          organizationId,
          provider,
          model,
          promptTokens: tokenUsage.promptTokens || 0,
          completionTokens: tokenUsage.completionTokens || 0,
          totalTokens: tokenUsage.totalTokens || 0,
          cost: this.calculateCost(provider, model, tokenUsage),
        },
      });
    } catch (error) {
      this.logger.error('Failed to log usage:', error);
    }
  }

  async getAvailableProviders() {
    const providers = [
      {
        id: 'openai',
        name: 'OpenAI',
        description: 'GPT models by OpenAI',
        models: [
          'gpt-4',
          'gpt-4-turbo',
          'gpt-4-turbo-preview',
          'gpt-3.5-turbo',
          'gpt-3.5-turbo-16k'
        ],
        capabilities: ['chat', 'function_calling', 'vision'],
        isConfigured: !!process.env.OPENAI_API_KEY,
        health: this.providerHealth.get('openai'),
      },
      {
        id: 'anthropic',
        name: 'Anthropic (Claude)',
        description: 'Claude models by Anthropic',
        models: [
          'claude-3-opus-20240229',
          'claude-3-sonnet-20240229',
          'claude-3-haiku-20240307',
          'claude-3-5-sonnet-20241022'
        ],
        capabilities: ['chat', 'long_context', 'reasoning'],
        isConfigured: !!process.env.ANTHROPIC_API_KEY,
        health: this.providerHealth.get('anthropic'),
      },
      {
        id: 'groq',
        name: 'Groq',
        description: 'Fast inference with Groq',
        models: [
          'mixtral-8x7b-32768',
          'llama2-70b-4096',
          'llama-3.1-8b-instant',
          'llama-3.1-70b-versatile'
        ],
        capabilities: ['chat', 'fast_inference'],
        isConfigured: !!process.env.GROQ_API_KEY,
        health: this.providerHealth.get('groq'),
      },
      {
        id: 'mistral',
        name: 'Mistral AI',
        description: 'Mistral models',
        models: [
          'mistral-large-latest',
          'mistral-medium-latest',
          'mistral-small-latest',
          'open-mixtral-8x7b'
        ],
        capabilities: ['chat', 'multilingual', 'coding'],
        isConfigured: !!process.env.MISTRAL_API_KEY,
        health: this.providerHealth.get('mistral'),
      },
      {
        id: 'gemini',
        name: 'Google Gemini',
        description: 'Google Gemini models',
        models: [
          'gemini-pro',
          'gemini-pro-vision',
          'gemini-1.5-pro'
        ],
        capabilities: ['chat', 'vision', 'multimodal'],
        isConfigured: !!process.env.GOOGLE_API_KEY,
        health: this.providerHealth.get('gemini'),
      },
      {
        id: 'deepseek',
        name: 'DeepSeek',
        description: 'DeepSeek AI models',
        models: [
          'deepseek-chat',
          'deepseek-coder'
        ],
        capabilities: ['chat', 'coding'],
        isConfigured: !!process.env.DEEPSEEK_API_KEY,
        health: this.providerHealth.get('deepseek'),
      },
      {
        id: 'together',
        name: 'Together AI',
        description: 'Open source models via Together',
        models: [
          'meta-llama/Llama-2-70b-chat-hf',
          'mistralai/Mixtral-8x7B-Instruct-v0.1',
          'togethercomputer/RedPajama-INCITE-Chat-3B-v1'
        ],
        capabilities: ['chat', 'open_source'],
        isConfigured: !!process.env.TOGETHER_API_KEY,
        health: this.providerHealth.get('together'),
      },
      {
        id: 'perplexity',
        name: 'Perplexity',
        description: 'Perplexity AI with online search',
        models: [
          'llama-3.1-sonar-small-128k-online',
          'llama-3.1-sonar-large-128k-online',
          'llama-3.1-sonar-huge-128k-online'
        ],
        capabilities: ['chat', 'search', 'online'],
        isConfigured: !!process.env.PERPLEXITY_API_KEY,
        health: this.providerHealth.get('perplexity'),
      },
    ];

    return providers.filter(p => p.isConfigured);
  }

  async getProviderHealth() {
    return Array.from(this.providerHealth.entries()).map(([provider, health]) => ({
      provider,
      ...health,
      rateLimitStatus: this.getRateLimitStatus(provider),
    }));
  }

  private getRateLimitStatus(provider: string) {
    const limit = this.rateLimits.get(provider);
    if (!limit) return { remaining: this.getProviderRateLimit(provider), resetTime: null };
    
    const remaining = Math.max(0, this.getProviderRateLimit(provider) - limit.count);
    return {
      remaining,
      resetTime: new Date(limit.resetTime),
      isLimited: remaining === 0,
    };
  }

  async getUsageStatistics(organizationId: string) {
    const last30Days = new Date();
    last30Days.setDate(last30Days.getDate() - 30);

    const usage = await this.prisma.providerUsage.groupBy({
      by: ['provider', 'model'],
      where: {
        organizationId,
        createdAt: { gte: last30Days },
      },
      _sum: {
        promptTokens: true,
        completionTokens: true,
        totalTokens: true,
        cost: true,
      },
      _count: { id: true },
    });

    return usage.map(u => ({
      provider: u.provider,
      model: u.model,
      requests: u._count.id,
      promptTokens: u._sum.promptTokens || 0,
      completionTokens: u._sum.completionTokens || 0,
      totalTokens: u._sum.totalTokens || 0,
      cost: u._sum.cost || 0,
    }));
  }
}