import { Controller, Get, Post, Body, Param, Put, Delete, UseGuards } from '@nestjs/common';
import { ApiTags, ApiBearerAuth } from '@nestjs/swagger';
import { ToolsService } from './tools.service';
import { CreateToolDto, UpdateToolDto } from './dto/tool.dto';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { CurrentUser } from '../auth/decorators/current-user.decorator';
import { User } from '../auth/entities/user.entity';

@ApiTags('tools')
@Controller('tools')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth()
export class ToolsController {
  constructor(private readonly toolsService: ToolsService) {}

  @Post()
  async create(@Body() createToolDto: CreateToolDto, @CurrentUser() user: User) {
    return this.toolsService.create(createToolDto, user.organizationId);
  }

  @Get()
  async findAll(@CurrentUser() user: User) {
    return this.toolsService.findAll(user.organizationId);
  }

  @Get('marketplace')
  async getMarketplace() {
    return this.toolsService.getMarketplace();
  }

  @Get(':id')
  async findOne(@Param('id') id: string, @CurrentUser() user: User) {
    return this.toolsService.findOne(id, user.organizationId);
  }

  @Put(':id')
  async update(
    @Param('id') id: string,
    @Body() updateToolDto: UpdateToolDto,
    @CurrentUser() user: User,
  ) {
    return this.toolsService.update(id, updateToolDto, user.organizationId);
  }

  @Delete(':id')
  async remove(@Param('id') id: string, @CurrentUser() user: User) {
    return this.toolsService.remove(id, user.organizationId);
  }
}