import { Injectable } from '@nestjs/common';
import { Socket } from 'socket.io';
import { JwtService } from '@nestjs/jwt';
import { PrismaService } from '../../common/prisma/prisma.service';

@Injectable()
export class ApixService {
  constructor(
    private prisma: PrismaService,
    private jwtService: JwtService,
  ) {}

  async authenticateSocket(client: Socket) {
    try {
      const token = client.handshake.auth?.token || client.handshake.headers?.authorization?.replace('Bearer ', '');
      
      if (!token) {
        throw new Error('No token provided');
      }

      const payload = this.jwtService.verify(token);
      const user = await this.prisma.user.findUnique({
        where: { id: payload.sub },
        include: { organization: true },
      });

      if (!user || !user.isActive) {
        throw new Error('User not found or inactive');
      }

      return user;
    } catch (error) {
      throw new Error('Authentication failed');
    }
  }

  emitEvent(event: string, data: any) {
    // Global event emission logic
    console.log(`APIX Event: ${event}`, data);
  }

  async handleAgentMessage(data: any, user: any) {
    // Process agent messages through APIX protocol
    const message = await this.prisma.message.create({
      data: {
        role: 'USER',
        content: data.content,
        sessionId: data.sessionId,
        metadata: { timestamp: new Date().toISOString() },
      },
    });

    this.emitEvent('agent-message-received', {
      messageId: message.id,
      sessionId: data.sessionId,
      organizationId: user.organizationId,
    });

    return message;
  }

  async emitToSession(sessionId: string, event: string, data: any) {
    // Emit events to specific session rooms
    console.log(`Session ${sessionId} Event: ${event}`, data);
  }

  async emitToOrganization(organizationId: string, event: string, data: any) {
    // Emit events to organization rooms
    console.log(`Organization ${organizationId} Event: ${event}`, data);
  }
}