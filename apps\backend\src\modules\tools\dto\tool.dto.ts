import { IsString, IsEnum, IsArray, IsBoolean, IsOptional } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';
import { ToolCategory, ToolParameter, ToolAuthentication } from '@synapseai/shared';

export class CreateToolDto {
  @ApiProperty()
  @IsString()
  name: string;

  @ApiProperty()
  @IsString()
  description: string;

  @ApiProperty({ enum: ToolCategory })
  @IsEnum(ToolCategory)
  category: ToolCategory;

  @ApiProperty()
  @IsString()
  version: string;

  @ApiProperty()
  @IsArray()
  parameters: ToolParameter[];

  @ApiProperty()
  authentication: ToolAuthentication;

  @ApiProperty({ required: false })
  @IsBoolean()
  @IsOptional()
  isPublic?: boolean;
}

export class UpdateToolDto {
  @ApiProperty({ required: false })
  @IsString()
  @IsOptional()
  name?: string;

  @ApiProperty({ required: false })
  @IsString()
  @IsOptional()
  description?: string;

  @ApiProperty({ enum: ToolCategory, required: false })
  @IsEnum(ToolCategory)
  @IsOptional()
  category?: ToolCategory;

  @ApiProperty({ required: false })
  @IsString()
  @IsOptional()
  version?: string;

  @ApiProperty({ required: false })
  @IsArray()
  @IsOptional()
  parameters?: ToolParameter[];

  @ApiProperty({ required: false })
  @IsOptional()
  authentication?: ToolAuthentication;

  @ApiProperty({ required: false })
  @IsBoolean()
  @IsOptional()
  isPublic?: boolean;
}