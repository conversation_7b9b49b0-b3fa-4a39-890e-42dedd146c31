import { IsString, IsObject, IsOptional, IsBoolean, IsArray } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export class CreateWorkflowDto {
  @ApiProperty({ example: 'Customer Onboarding Flow' })
  @IsString()
  name: string;

  @ApiProperty({ example: 'Automated customer onboarding process' })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiProperty({
    example: {
      nodes: [
        { id: '1', type: 'trigger', position: { x: 0, y: 0 } },
        { id: '2', type: 'agent', position: { x: 200, y: 0 } }
      ],
      edges: [
        { source: '1', target: '2' }
      ]
    }
  })
  @IsObject()
  definition: Record<string, any>;

  @ApiProperty({ example: true })
  @IsOptional()
  @IsBoolean()
  isActive?: boolean;
}

export class UpdateWorkflowDto {
  @ApiProperty({ example: 'Updated Workflow Name' })
  @IsOptional()
  @IsString()
  name?: string;

  @ApiProperty({ example: 'Updated description' })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiProperty()
  @IsOptional()
  @IsObject()
  definition?: Record<string, any>;

  @ApiProperty()
  @IsOptional()
  @IsBoolean()
  isActive?: boolean;
}

export class ExecuteWorkflowDto {
  @ApiProperty({ example: { userId: '123', email: '<EMAIL>' } })
  @IsObject()
  input: Record<string, any>;

  @ApiProperty({ example: 'session_123', required: false })
  @IsOptional()
  @IsString()
  sessionId?: string;
}