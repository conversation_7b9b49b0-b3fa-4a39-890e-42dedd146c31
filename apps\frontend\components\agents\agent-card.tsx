import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { <PERSON><PERSON>, MessageCircle, Settings, Play } from 'lucide-react'
import Link from 'next/link'

interface AgentCardProps {
  agent: any
  onUpdate: () => void
}

export function AgentCard({ agent, onUpdate }: AgentCardProps) {
  return (
    <Card className="hover:shadow-md transition-shadow">
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <div className="flex items-center space-x-2">
          <Bot className="h-5 w-5 text-primary" />
          <CardTitle className="text-lg">{agent.name}</CardTitle>
        </div>
        <Badge variant={agent.isActive ? 'default' : 'secondary'}>
          {agent.isActive ? 'Active' : 'Inactive'}
        </Badge>
      </CardHeader>
      <CardContent>
        <CardDescription className="mb-4">
          {agent.description || 'No description provided'}
        </CardDescription>
        
        <div className="space-y-2 mb-4">
          <div className="flex justify-between text-sm">
            <span className="text-muted-foreground">Provider:</span>
            <span className="font-medium">{agent.provider}</span>
          </div>
          <div className="flex justify-between text-sm">
            <span className="text-muted-foreground">Model:</span>
            <span className="font-medium">{agent.model}</span>
          </div>
          <div className="flex justify-between text-sm">
            <span className="text-muted-foreground">Type:</span>
            <span className="font-medium">{agent.type}</span>
          </div>
          {agent.sessions && (
            <div className="flex justify-between text-sm">
              <span className="text-muted-foreground">Active Sessions:</span>
              <span className="font-medium">{agent.sessions.length}</span>
            </div>
          )}
        </div>

        <div className="flex gap-2">
          <Button asChild size="sm" className="flex-1">
            <Link href={`/agents/${agent.id}`}>
              <MessageCircle className="h-4 w-4 mr-2" />
              Chat
            </Link>
          </Button>
          <Button variant="outline" size="sm">
            <Settings className="h-4 w-4" />
          </Button>
        </div>
      </CardContent>
    </Card>
  )
}