import { Controller, Get, Post, Body, Param, UseGuards, Request } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';
import { ProvidersService } from './providers.service';
import { AuthGuard } from '../../common/guards/auth.guard';
import { TestProviderDto } from './dto/provider.dto';

@ApiTags('Providers')
@ApiBearerAuth()
@UseGuards(AuthGuard)
@Controller('providers')
export class ProvidersController {
  constructor(private readonly providersService: ProvidersService) {}

  @Get()
  @ApiOperation({ summary: 'Get available AI providers and models' })
  @ApiResponse({ status: 200, description: 'List of available providers' })
  getProviders() {
    return this.providersService.getAvailableProviders();
  }

  @Get('health')
  @ApiOperation({ summary: 'Get provider health status' })
  @ApiResponse({ status: 200, description: 'Provider health metrics' })
  getProviderHealth() {
    return this.providersService.getProviderHealth();
  }

  @Get('usage')
  @ApiOperation({ summary: 'Get usage statistics' })
  @ApiResponse({ status: 200, description: 'Provider usage statistics' })
  getUsageStatistics(@Request() req) {
    return this.providersService.getUsageStatistics(req.user.organizationId);
  }

  @Post('test')
  @ApiOperation({ summary: 'Test provider configuration' })
  @ApiResponse({ status: 200, description: 'Provider test successful' })
  async testProvider(@Body() testDto: TestProviderDto, @Request() req) {
    const response = await this.providersService.generateResponse({
      provider: testDto.provider,
      model: testDto.model,
      messages: [{ role: 'user', content: testDto.message || 'Hello, this is a test message.' }],
      temperature: 0.1,
      maxTokens: 50,
      organizationId: req.user.organizationId,
    });

    return {
      success: true,
      provider: response.provider,
      model: response.model,
      response: response.content,
      latency: response.latency,
      tokenUsage: response.tokenUsage,
      cost: response.cost,
    };
  }
}