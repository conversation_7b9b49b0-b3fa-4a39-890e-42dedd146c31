import { Card, CardContent, CardDescription, Card<PERSON><PERSON>er, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Workflow, Play, Settings, Activity, Edit, Eye } from 'lucide-react'
import { useState } from 'react'
import { useMutation } from 'react-query'
import { api } from '@/lib/api'
import toast from 'react-hot-toast'
import Link from 'next/link'

interface WorkflowCardProps {
  workflow: any
  onUpdate: () => void
}

export function WorkflowCard({ workflow, onUpdate }: WorkflowCardProps) {
  const executeMutation = useMutation(
    (input: any) => api.post(`/workflows/${workflow.id}/execute`, { input }),
    {
      onSuccess: () => {
        toast.success('Workflow executed successfully!')
        onUpdate()
      },
      onError: () => {
        toast.error('Failed to execute workflow')
      },
    }
  )

  const handleQuickExecute = () => {
    // Demo execution with sample input
    executeMutation.mutate({ message: 'Test workflow execution' })
  }

  const getNodeCount = () => {
    return workflow.definition?.nodes?.length || workflow.nodes?.length || 0
  }

  const getConnectionCount = () => {
    return workflow.definition?.edges?.length || 0
  }

  return (
    <Card className="hover:shadow-md transition-shadow">
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <div className="flex items-center space-x-2">
          <Workflow className="h-5 w-5 text-primary" />
          <CardTitle className="text-lg">{workflow.name}</CardTitle>
        </div>
        <Badge variant={workflow.isActive ? 'default' : 'secondary'}>
          {workflow.isActive ? 'Active' : 'Inactive'}
        </Badge>
      </CardHeader>
      <CardContent>
        <CardDescription className="mb-4">
          {workflow.description || 'No description provided'}
        </CardDescription>
        
        <div className="space-y-2 mb-4">
          <div className="flex justify-between text-sm">
            <span className="text-muted-foreground">Version:</span>
            <span className="font-medium">v{workflow.version || 1}</span>
          </div>
          
          <div className="flex justify-between text-sm">
            <span className="text-muted-foreground">Nodes:</span>
            <span className="font-medium">{getNodeCount()}</span>
          </div>
          
          <div className="flex justify-between text-sm">
            <span className="text-muted-foreground">Connections:</span>
            <span className="font-medium">{getConnectionCount()}</span>
          </div>
          
          {workflow.executions && (
            <div className="flex justify-between text-sm">
              <span className="text-muted-foreground">Recent Runs:</span>
              <span className="font-medium">{workflow.executions.length}</span>
            </div>
          )}
        </div>

        <div className="flex gap-2">
          <Button 
            size="sm" 
            onClick={handleQuickExecute}
            disabled={executeMutation.isLoading}
          >
            <Play className="h-4 w-4 mr-2" />
            {executeMutation.isLoading ? 'Running...' : 'Run'}
          </Button>
          <Button variant="outline" size="sm" asChild>
            <Link href={`/workflows/${workflow.id}/edit`}>
              <Edit className="h-4 w-4 mr-2" />
              Edit
            </Link>
          </Button>
          <Button variant="outline" size="sm">
            <Eye className="h-4 w-4" />
          </Button>
        </div>

        {workflow.executions && workflow.executions.length > 0 && (
          <div className="mt-4 pt-4 border-t">
            <div className="flex items-center gap-2 mb-2">
              <Activity className="h-4 w-4 text-muted-foreground" />
              <span className="text-sm font-medium">Recent Activity</span>
            </div>
            <div className="space-y-1">
              {workflow.executions.slice(0, 3).map((execution: any) => (
                <div key={execution.id} className="flex justify-between items-center text-xs">
                  <span className="text-muted-foreground">
                    {new Date(execution.createdAt).toLocaleDateString()}
                  </span>
                  <Badge 
                    variant={execution.status === 'COMPLETED' ? 'default' : 
                            execution.status === 'FAILED' ? 'destructive' : 'secondary'}
                    className="text-xs"
                  >
                    {execution.status}
                  </Badge>
                </div>
              ))}
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  )
}