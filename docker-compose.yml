version: '3.8'

services:
  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    environment:
      POSTGRES_DB: synapseai
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres
    volumes:
      - postgres_data:/var/lib/postgresql/data
    ports:
      - "5432:5432"
    networks:
      - synapseai

  # Redis Cache
  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - synapseai

  # Backend API
  backend:
    build:
      context: .
      dockerfile: apps/backend/Dockerfile
    environment:
      DATABASE_URL: ********************************************/synapseai
      REDIS_URL: redis://redis:6379
      JWT_SECRET: your-production-jwt-secret
      NODE_ENV: production
      PORT: 3001
    ports:
      - "3001:3001"
    depends_on:
      - postgres
      - redis
    networks:
      - synapseai
    volumes:
      - ./apps/backend:/app
      - /app/node_modules

  # Frontend Application
  frontend:
    build:
      context: .
      dockerfile: apps/frontend/Dockerfile
    environment:
      NEXT_PUBLIC_API_URL: http://localhost:3001
      NEXT_PUBLIC_WS_URL: ws://localhost:3001
      NODE_ENV: production
    ports:
      - "3000:3000"
    depends_on:
      - backend
    networks:
      - synapseai
    volumes:
      - ./apps/frontend:/app
      - /app/node_modules

networks:
  synapseai:
    driver: bridge

volumes:
  postgres_data:
  redis_data: