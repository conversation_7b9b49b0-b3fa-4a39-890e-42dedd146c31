import { Entity, PrimaryGeneratedColumn, Column, CreateDateColumn, UpdateDateColumn } from 'typeorm';
import { AgentType, AIProvider, AgentStatus, AgentMemory } from '@synapseai/shared';

@Entity('agents')
export class Agent {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column()
  name: string;

  @Column('text')
  description: string;

  @Column({
    type: 'enum',
    enum: AgentType,
  })
  type: AgentType;

  @Column({
    type: 'enum',
    enum: AIProvider,
  })
  aiProvider: AIProvider;

  @Column()
  model: string;

  @Column('text')
  systemPrompt: string;

  @Column('text', { array: true, default: [] })
  tools: string[];

  @Column('jsonb')
  memory: AgentMemory;

  @Column({
    type: 'enum',
    enum: AgentStatus,
    default: AgentStatus.IDLE,
  })
  status: AgentStatus;

  @Column('uuid')
  organizationId: string;

  @Column('uuid')
  createdBy: string;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
}