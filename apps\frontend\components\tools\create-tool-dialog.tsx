'use client'

import { useState } from 'react'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { api } from '@/lib/api'
import toast from 'react-hot-toast'

const createToolSchema = z.object({
  name: z.string().min(1, 'Name is required'),
  description: z.string().optional(),
  type: z.enum(['FUNCTION_CALL', 'REST_API', 'RAG_RETRIEVAL', 'BROWSER_ACTION', 'DATABASE_QUERY']),
  config: z.string().min(1, 'Configuration is required'),
  inputSchema: z.string().min(1, 'Input schema is required'),
  outputSchema: z.string().optional(),
})

type CreateToolForm = z.infer<typeof createToolSchema>

interface CreateToolDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  onSuccess: () => void
}

export function CreateToolDialog({ open, onOpenChange, onSuccess }: CreateToolDialogProps) {
  const [isLoading, setIsLoading] = useState(false)

  const {
    register,
    handleSubmit,
    formState: { errors },
    setValue,
    watch,
    reset,
  } = useForm<CreateToolForm>({
    resolver: zodResolver(createToolSchema),
    defaultValues: {
      type: 'FUNCTION_CALL',
      config: '{}',
      inputSchema: '{"type": "object", "properties": {}, "required": []}',
      outputSchema: '{"type": "object", "properties": {}}',
    },
  })

  const toolType = watch('type')

  const onSubmit = async (data: CreateToolForm) => {
    setIsLoading(true)
    try {
      const payload = {
        ...data,
        config: JSON.parse(data.config),
        inputSchema: JSON.parse(data.inputSchema),
        outputSchema: data.outputSchema ? JSON.parse(data.outputSchema) : undefined,
      }
      await api.post('/tools', payload)
      toast.success('Tool created successfully!')
      reset()
      onSuccess()
    } catch (error) {
      toast.error('Failed to create tool')
    } finally {
      setIsLoading(false)
    }
  }

  const getConfigTemplate = (type: string) => {
    switch (type) {
      case 'FUNCTION_CALL':
        return JSON.stringify({
          functionName: 'calculate',
          parameters: ['operation', 'a', 'b']
        }, null, 2)
      case 'REST_API':
        return JSON.stringify({
          url: 'https://api.example.com/data',
          method: 'GET',
          headers: { 'Content-Type': 'application/json' }
        }, null, 2)
      case 'RAG_RETRIEVAL':
        return JSON.stringify({
          knowledgeBaseId: 'kb-123',
          maxResults: 5
        }, null, 2)
      default:
        return '{}'
    }
  }

  const getInputSchemaTemplate = (type: string) => {
    switch (type) {
      case 'FUNCTION_CALL':
        return JSON.stringify({
          type: 'object',
          properties: {
            operation: { type: 'string', enum: ['add', 'subtract', 'multiply', 'divide'] },
            a: { type: 'number' },
            b: { type: 'number' }
          },
          required: ['operation', 'a', 'b']
        }, null, 2)
      case 'REST_API':
        return JSON.stringify({
          type: 'object',
          properties: {
            endpoint: { type: 'string' },
            params: { type: 'object' }
          },
          required: ['endpoint']
        }, null, 2)
      case 'RAG_RETRIEVAL':
        return JSON.stringify({
          type: 'object',
          properties: {
            query: { type: 'string', description: 'Search query' }
          },
          required: ['query']
        }, null, 2)
      default:
        return '{"type": "object", "properties": {}, "required": []}'
    }
  }

  const handleTypeChange = (type: string) => {
    setValue('type', type as any)
    setValue('config', getConfigTemplate(type))
    setValue('inputSchema', getInputSchemaTemplate(type))
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Create New Tool</DialogTitle>
          <DialogDescription>
            Set up a new tool with specific functionality and configuration.
          </DialogDescription>
        </DialogHeader>

        <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="name">Name</Label>
              <Input
                id="name"
                placeholder="Weather API Tool"
                {...register('name')}
                className={errors.name ? 'border-red-500' : ''}
              />
              {errors.name && (
                <p className="text-sm text-red-500">{errors.name.message}</p>
              )}
            </div>
            <div className="space-y-2">
              <Label htmlFor="type">Type</Label>
              <Select onValueChange={handleTypeChange}>
                <SelectTrigger>
                  <SelectValue placeholder="Select tool type" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="FUNCTION_CALL">Function Call</SelectItem>
                  <SelectItem value="REST_API">REST API</SelectItem>
                  <SelectItem value="RAG_RETRIEVAL">RAG Retrieval</SelectItem>
                  <SelectItem value="BROWSER_ACTION">Browser Action</SelectItem>
                  <SelectItem value="DATABASE_QUERY">Database Query</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="description">Description</Label>
            <Input
              id="description"
              placeholder="Brief description of the tool's purpose"
              {...register('description')}
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="config">Configuration (JSON)</Label>
            <Textarea
              id="config"
              placeholder="Tool configuration..."
              rows={6}
              {...register('config')}
              className={`font-mono text-sm ${errors.config ? 'border-red-500' : ''}`}
            />
            {errors.config && (
              <p className="text-sm text-red-500">{errors.config.message}</p>
            )}
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="inputSchema">Input Schema (JSON)</Label>
              <Textarea
                id="inputSchema"
                placeholder="Input validation schema..."
                rows={8}
                {...register('inputSchema')}
                className={`font-mono text-sm ${errors.inputSchema ? 'border-red-500' : ''}`}
              />
              {errors.inputSchema && (
                <p className="text-sm text-red-500">{errors.inputSchema.message}</p>
              )}
            </div>
            <div className="space-y-2">
              <Label htmlFor="outputSchema">Output Schema (JSON) - Optional</Label>
              <Textarea
                id="outputSchema"
                placeholder="Output schema definition..."
                rows={8}
                {...register('outputSchema')}
                className="font-mono text-sm"
              />
            </div>
          </div>

          <div className="flex justify-end gap-2 pt-4">
            <Button type="button" variant="outline" onClick={() => onOpenChange(false)}>
              Cancel
            </Button>
            <Button type="submit" disabled={isLoading}>
              {isLoading ? 'Creating...' : 'Create Tool'}
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  )
}