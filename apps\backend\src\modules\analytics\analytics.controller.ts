import { Controller, Get, Query, UseGuards, Request } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth, ApiQuery } from '@nestjs/swagger';
import { AnalyticsService } from './analytics.service';
import { AuthGuard } from '../../common/guards/auth.guard';

@ApiTags('Analytics')
@ApiBearerAuth()
@UseGuards(AuthGuard)
@Controller('analytics')
export class AnalyticsController {
  constructor(private readonly analyticsService: AnalyticsService) {}

  @Get('dashboard')
  @ApiOperation({ summary: 'Get dashboard analytics' })
  @ApiResponse({ status: 200, description: 'Dashboard metrics and trends' })
  getDashboard(@Request() req) {
    return this.analyticsService.getDashboardMetrics(req.user.organizationId);
  }

  @Get('workflows')
  @ApiOperation({ summary: 'Get workflow analytics' })
  @ApiResponse({ status: 200, description: 'Workflow metrics and usage' })
  getWorkflows(@Request() req) {
    return this.analyticsService.getWorkflowAnalytics(req.user.organizationId);
  }

  @Get('agents')
  @ApiOperation({ summary: 'Get agent analytics' })
  @ApiResponse({ status: 200, description: 'Agent metrics and usage' })
  getAgents(@Request() req) {
    return this.analyticsService.getAgentAnalytics(req.user.organizationId);
  }

  @Get('tools')
  @ApiOperation({ summary: 'Get tool analytics' })
  @ApiResponse({ status: 200, description: 'Tool metrics and usage' })
  getTools(@Request() req) {
    return this.analyticsService.getToolAnalytics(req.user.organizationId);
  }

  @Get('usage-report')
  @ApiOperation({ summary: 'Get usage report for date range' })
  @ApiQuery({ name: 'startDate', required: true, type: String })
  @ApiQuery({ name: 'endDate', required: true, type: String })
  @ApiResponse({ status: 200, description: 'Usage report for specified period' })
  getUsageReport(
    @Request() req,
    @Query('startDate') startDate: string,
    @Query('endDate') endDate: string,
  ) {
    const start = new Date(startDate);
    const end = new Date(endDate);
    return this.analyticsService.getUsageReport(req.user.organizationId, start, end);
  }
}