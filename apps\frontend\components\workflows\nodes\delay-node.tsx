import { memo } from 'react'
import { Handle, Position, NodeProps } from 'reactflow'
import { Card, CardContent } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Clock } from 'lucide-react'

export const DelayNode = memo(({ data, selected }: NodeProps) => {
  const formatDuration = () => {
    const duration = data.duration || 1000
    const unit = data.unit || 'ms'
    return `${duration}${unit}`
  }

  return (
    <Card className={`min-w-[200px] ${selected ? 'ring-2 ring-primary' : ''} border-blue-200`}>
      <CardContent className="p-3">
        <div className="flex items-center gap-2 mb-2">
          <Clock className="h-4 w-4 text-blue-500" />
          <span className="font-medium text-sm">{data.label || 'Delay'}</span>
        </div>
        
        <Badge variant="outline" className="text-xs">
          Wait {formatDuration()}
        </Badge>
        
        <p className="text-xs text-muted-foreground mt-2">
          Pauses execution for specified duration
        </p>
      </CardContent>
      
      <Handle type="target" position={Position.Left} className="w-3 h-3" />
      <Handle type="source" position={Position.Right} className="w-3 h-3" />
    </Card>
  )
})