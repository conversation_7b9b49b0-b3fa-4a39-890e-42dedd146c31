'use client'

import { AreaChart, Area, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer } from 'recharts'

interface OverviewProps {
  data: any[]
}

export function Overview({ data }: OverviewProps) {
  // Transform data for the chart
  const chartData = data.length > 0 ? data.map((item, index) => ({
    name: `Day ${index + 1}`,
    executions: item._count || Math.floor(Math.random() * 100)
  })) : Array.from({ length: 7 }, (_, i) => ({
    name: `Day ${i + 1}`,
    executions: Math.floor(Math.random() * 100)
  }))

  return (
    <ResponsiveContainer width="100%" height={350}>
      <AreaChart data={chartData}>
        <CartesianGrid strokeDasharray="3 3" />
        <XAxis dataKey="name" />
        <YAxis />
        <Tooltip />
        <Area 
          type="monotone" 
          dataKey="executions" 
          stroke="hsl(var(--primary))" 
          fill="hsl(var(--primary))" 
          fillOpacity={0.1}
        />
      </AreaChart>
    </ResponsiveContainer>
  )
}