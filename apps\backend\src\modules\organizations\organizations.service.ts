import { Injectable } from '@nestjs/common';
import { PrismaService } from '../../common/prisma/prisma.service';

@Injectable()
export class OrganizationsService {
  constructor(private prisma: PrismaService) {}

  async findById(id: string) {
    return this.prisma.organization.findUnique({
      where: { id },
      include: {
        users: { where: { isActive: true } },
        agents: { where: { isActive: true } },
        tools: { where: { isActive: true } },
        workflows: { where: { isActive: true } },
      },
    });
  }

  async getStats(organizationId: string) {
    const [agentsCount, toolsCount, workflowsCount, sessionsCount] = await Promise.all([
      this.prisma.agent.count({ where: { organizationId, isActive: true } }),
      this.prisma.tool.count({ where: { organizationId, isActive: true } }),
      this.prisma.workflow.count({ where: { organizationId, isActive: true } }),
      this.prisma.session.count({ where: { organizationId, status: 'ACTIVE' } }),
    ]);

    return {
      agents: agentsCount,
      tools: toolsCount,
      workflows: workflowsCount,
      activeSessions: sessionsCount,
    };
  }
}