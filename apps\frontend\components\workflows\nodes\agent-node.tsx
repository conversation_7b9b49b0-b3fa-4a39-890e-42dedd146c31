import { memo } from 'react'
import { Handle, Position, NodeProps } from 'reactflow'
import { Card, CardContent } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Bot, AlertCircle } from 'lucide-react'

export const AgentNode = memo(({ data, selected }: NodeProps) => {
  const hasError = !data.agentId

  return (
    <Card className={`min-w-[200px] ${selected ? 'ring-2 ring-primary' : ''} ${hasError ? 'border-red-500' : ''}`}>
      <CardContent className="p-3">
        <div className="flex items-center gap-2 mb-2">
          <Bot className="h-4 w-4 text-blue-500" />
          <span className="font-medium text-sm">{data.label || 'AI Agent'}</span>
          {hasError && <AlertCircle className="h-3 w-3 text-red-500" />}
        </div>
        
        {data.agentId && (
          <Badge variant="outline" className="text-xs">
            Connected
          </Badge>
        )}
        
        {!data.agentId && (
          <Badge variant="destructive" className="text-xs">
            No Agent Selected
          </Badge>
        )}
        
        {data.instructions && (
          <p className="text-xs text-muted-foreground mt-2 line-clamp-2">
            {data.instructions}
          </p>
        )}
      </CardContent>
      
      <Handle type="target" position={Position.Left} className="w-3 h-3" />
      <Handle type="source" position={Position.Right} className="w-3 h-3" />
    </Card>
  )
})