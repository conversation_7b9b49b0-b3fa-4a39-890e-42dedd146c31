{"name": "synapseai-platform", "version": "1.0.0", "private": true, "workspaces": ["apps/*", "packages/*"], "scripts": {"dev": "concurrently \"npm run dev:backend\" \"npm run dev:frontend\"", "dev:backend": "cd apps/backend && npm run start:dev", "dev:frontend": "cd apps/frontend && npm run dev", "build": "npm run build:backend && npm run build:frontend", "build:backend": "cd apps/backend && npm run build", "build:frontend": "cd apps/frontend && npm run build", "db:generate": "cd apps/backend && npx prisma generate", "db:push": "cd apps/backend && npx prisma db push", "db:studio": "cd apps/backend && npx prisma studio", "test": "npm run test:backend && npm run test:frontend", "test:backend": "cd apps/backend && npm run test", "test:frontend": "cd apps/frontend && npm run test"}, "devDependencies": {"concurrently": "^8.2.2", "@types/node": "^20.10.0", "typescript": "^5.3.0"}}