import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { ThrottlerModule } from '@nestjs/throttler';
import { PrismaModule } from './common/prisma/prisma.module';
import { AuthModule } from './modules/auth/auth.module';
import { UsersModule } from './modules/users/users.module';
import { OrganizationsModule } from './modules/organizations/organizations.module';
import { AgentsModule } from './modules/agents/agents.module';
import { ToolsModule } from './modules/tools/tools.module';
import { WorkflowsModule } from './modules/workflows/workflows.module';
import { SessionsModule } from './modules/sessions/sessions.module';
import { ProvidersModule } from './modules/providers/providers.module';
import { KnowledgeModule } from './modules/knowledge/knowledge.module';
import { ApixModule } from './modules/apix/apix.module';
import { AnalyticsModule } from './modules/analytics/analytics.module';

@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
      envFilePath: '.env',
    }),
    ThrottlerModule.forRoot([
      {
        ttl: 60000,
        limit: 100,
      },
    ]),
    PrismaModule,
    AuthModule,
    UsersModule,
    OrganizationsModule,
    AgentsModule,
    ToolsModule,
    WorkflowsModule,
    SessionsModule,
    ProvidersModule,
    KnowledgeModule,
    ApixModule,
    AnalyticsModule,
  ],
})
export class AppModule {}