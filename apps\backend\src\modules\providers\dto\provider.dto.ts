import { IsString, IsOptional } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export class TestProviderDto {
  @ApiProperty({ example: 'openai' })
  @IsString()
  provider: string;

  @ApiProperty({ example: 'gpt-3.5-turbo' })
  @IsString()
  model: string;

  @ApiProperty({ example: 'Hello, this is a test message.', required: false })
  @IsOptional()
  @IsString()
  message?: string;
}