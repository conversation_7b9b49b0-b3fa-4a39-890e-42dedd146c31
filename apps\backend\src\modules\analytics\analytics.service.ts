import { Injectable } from '@nestjs/common';
import { PrismaService } from '../../common/prisma/prisma.service';

@Injectable()
export class AnalyticsService {
  constructor(private prisma: PrismaService) {}

  async getDashboardMetrics(organizationId: string) {
    const [
      agentsCount,
      toolsCount,
      workflowsCount,
      activeSessionsCount,
      totalExecutions,
      recentExecutions,
      successfulExecutions,
      failedExecutions,
    ] = await Promise.all([
      this.prisma.agent.count({ where: { organizationId, isActive: true } }),
      this.prisma.tool.count({ where: { organizationId, isActive: true } }),
      this.prisma.workflow.count({ where: { organizationId, isActive: true } }),
      this.prisma.session.count({ where: { organizationId, status: 'ACTIVE' } }),
      this.prisma.toolExecution.count({ where: { tool: { organizationId } } }),
      this.prisma.toolExecution.findMany({
        where: { tool: { organizationId } },
        include: { tool: true },
        orderBy: { createdAt: 'desc' },
        take: 10,
      }),
      this.prisma.toolExecution.count({ 
        where: { 
          tool: { organizationId },
          status: 'COMPLETED'
        } 
      }),
      this.prisma.toolExecution.count({ 
        where: { 
          tool: { organizationId },
          status: 'FAILED'
        } 
      }),
    ]);

    const last30Days = new Date();
    last30Days.setDate(last30Days.getDate() - 30);

    // Get execution trends for the last 30 days
    const executionTrends = await this.prisma.$queryRaw`
      SELECT 
        DATE(created_at) as date,
        COUNT(*) as executions,
        COUNT(CASE WHEN status = 'COMPLETED' THEN 1 END) as successful,
        COUNT(CASE WHEN status = 'FAILED' THEN 1 END) as failed
      FROM tool_executions te
      JOIN tools t ON te.tool_id = t.id
      WHERE t.organization_id = ${organizationId}
        AND te.created_at >= ${last30Days}
      GROUP BY DATE(created_at)
      ORDER BY date DESC
      LIMIT 30
    `;

    // Calculate success rate
    const successRate = totalExecutions > 0 
      ? ((successfulExecutions / totalExecutions) * 100).toFixed(1)
      : '0';

    // Calculate average response time (mock data for now)
    const avgResponseTime = await this.calculateAverageResponseTime(organizationId);

    return {
      overview: {
        agents: agentsCount,
        tools: toolsCount,
        workflows: workflowsCount,
        activeSessions: activeSessionsCount,
        totalExecutions,
        successRate: parseFloat(successRate),
        avgResponseTime,
      },
      recentActivity: recentExecutions,
      trends: executionTrends,
      performance: {
        successful: successfulExecutions,
        failed: failedExecutions,
        successRate: parseFloat(successRate),
      },
    };
  }

  async getWorkflowAnalytics(organizationId: string) {
    const [
      totalWorkflows,
      activeWorkflows,
      totalWorkflowExecutions,
      recentWorkflowExecutions,
    ] = await Promise.all([
      this.prisma.workflow.count({ where: { organizationId } }),
      this.prisma.workflow.count({ where: { organizationId, isActive: true } }),
      this.prisma.workflowExecution.count({ 
        where: { workflow: { organizationId } } 
      }),
      this.prisma.workflowExecution.findMany({
        where: { workflow: { organizationId } },
        include: { 
          workflow: true,
          toolExecutions: true 
        },
        orderBy: { createdAt: 'desc' },
        take: 20,
      }),
    ]);

    return {
      overview: {
        totalWorkflows,
        activeWorkflows,
        totalExecutions: totalWorkflowExecutions,
      },
      recentExecutions: recentWorkflowExecutions,
    };
  }

  async getAgentAnalytics(organizationId: string) {
    const [
      totalAgents,
      activeAgents,
      totalSessions,
      activeSessions,
      totalMessages,
    ] = await Promise.all([
      this.prisma.agent.count({ where: { organizationId } }),
      this.prisma.agent.count({ where: { organizationId, isActive: true } }),
      this.prisma.session.count({ where: { organizationId } }),
      this.prisma.session.count({ where: { organizationId, status: 'ACTIVE' } }),
      this.prisma.message.count({ 
        where: { session: { organizationId } } 
      }),
    ]);

    // Get agent usage statistics
    const agentUsage = await this.prisma.session.groupBy({
      by: ['agentId'],
      where: { organizationId, agentId: { not: null } },
      _count: { id: true },
      orderBy: { _count: { id: 'desc' } },
      take: 10,
    });

    return {
      overview: {
        totalAgents,
        activeAgents,
        totalSessions,
        activeSessions,
        totalMessages,
      },
      agentUsage,
    };
  }

  async getToolAnalytics(organizationId: string) {
    const [
      totalTools,
      activeTools,
      totalExecutions,
      successfulExecutions,
      failedExecutions,
    ] = await Promise.all([
      this.prisma.tool.count({ where: { organizationId } }),
      this.prisma.tool.count({ where: { organizationId, isActive: true } }),
      this.prisma.toolExecution.count({ where: { tool: { organizationId } } }),
      this.prisma.toolExecution.count({ 
        where: { 
          tool: { organizationId },
          status: 'COMPLETED'
        } 
      }),
      this.prisma.toolExecution.count({ 
        where: { 
          tool: { organizationId },
          status: 'FAILED'
        } 
      }),
    ]);

    // Get tool usage statistics
    const toolUsage = await this.prisma.toolExecution.groupBy({
      by: ['toolId'],
      where: { tool: { organizationId } },
      _count: { id: true },
      _avg: { duration: true },
      orderBy: { _count: { id: 'desc' } },
      take: 10,
    });

    return {
      overview: {
        totalTools,
        activeTools,
        totalExecutions,
        successfulExecutions,
        failedExecutions,
        successRate: totalExecutions > 0 
          ? ((successfulExecutions / totalExecutions) * 100).toFixed(1)
          : '0',
      },
      toolUsage,
    };
  }

  private async calculateAverageResponseTime(organizationId: string): Promise<number> {
    const executions = await this.prisma.toolExecution.findMany({
      where: { 
        tool: { organizationId },
        duration: { not: null },
        status: 'COMPLETED'
      },
      select: { duration: true },
      take: 100, // Last 100 executions
    });

    if (executions.length === 0) return 0;

    const totalDuration = executions.reduce((sum, exec) => sum + (exec.duration || 0), 0);
    return Math.round(totalDuration / executions.length);
  }

  async getUsageReport(organizationId: string, startDate: Date, endDate: Date) {
    const [
      toolExecutions,
      workflowExecutions,
      sessions,
      messages,
    ] = await Promise.all([
      this.prisma.toolExecution.count({
        where: {
          tool: { organizationId },
          createdAt: { gte: startDate, lte: endDate },
        },
      }),
      this.prisma.workflowExecution.count({
        where: {
          workflow: { organizationId },
          createdAt: { gte: startDate, lte: endDate },
        },
      }),
      this.prisma.session.count({
        where: {
          organizationId,
          createdAt: { gte: startDate, lte: endDate },
        },
      }),
      this.prisma.message.count({
        where: {
          session: { organizationId },
          createdAt: { gte: startDate, lte: endDate },
        },
      }),
    ]);

    return {
      period: {
        startDate: startDate.toISOString(),
        endDate: endDate.toISOString(),
      },
      usage: {
        toolExecutions,
        workflowExecutions,
        sessions,
        messages,
      },
    };
  }
}