'use client'

import { useState } from 'react'
import { useQuery } from 'react-query'
import { Button } from '@/components/ui/button'
import { WorkflowCard } from '@/components/workflows/workflow-card'
import { LoadingSpinner } from '@/components/ui/loading-spinner'
import { api } from '@/lib/api'
import { Plus, Workflow } from 'lucide-react'
import Link from 'next/link'

export default function WorkflowsPage() {
  const { data: workflows, isLoading, refetch } = useQuery('workflows', () =>
    api.get('/workflows').then(res => res.data)
  )

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <LoadingSpinner />
      </div>
    )
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Workflows</h1>
          <p className="text-muted-foreground">
            Orchestrate complex AI workflows and automations
          </p>
        </div>
        <Button asChild>
          <Link href="/workflows/create">
            <Plus className="h-4 w-4 mr-2" />
            Create Workflow
          </Link>
        </Button>
      </div>

      {workflows?.length === 0 ? (
        <div className="text-center py-12">
          <Workflow className="h-12 w-12 mx-auto mb-4 text-muted-foreground" />
          <h3 className="text-lg font-semibold mb-2">No workflows yet</h3>
          <p className="text-muted-foreground mb-4">
            Create your first workflow to automate complex AI processes
          </p>
          <Button asChild>
            <Link href="/workflows/create">
              <Plus className="h-4 w-4 mr-2" />
              Create Workflow
            </Link>
          </Button>
        </div>
      ) : (
        <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
          {workflows?.map((workflow: any) => (
            <WorkflowCard key={workflow.id} workflow={workflow} onUpdate={refetch} />
          ))}
        </div>
      )}
    </div>
  )
}