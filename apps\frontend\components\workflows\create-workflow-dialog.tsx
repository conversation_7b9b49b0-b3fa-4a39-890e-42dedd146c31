'use client'

import { useState } from 'react'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { api } from '@/lib/api'
import toast from 'react-hot-toast'

const createWorkflowSchema = z.object({
  name: z.string().min(1, 'Name is required'),
  description: z.string().optional(),
  definition: z.string().min(1, 'Definition is required'),
})

type CreateWorkflowForm = z.infer<typeof createWorkflowSchema>

interface CreateWorkflowDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  onSuccess: () => void
}

export function CreateWorkflowDialog({ open, onOpenChange, onSuccess }: CreateWorkflowDialogProps) {
  const [isLoading, setIsLoading] = useState(false)

  const {
    register,
    handleSubmit,
    formState: { errors },
    reset,
  } = useForm<CreateWorkflowForm>({
    resolver: zodResolver(createWorkflowSchema),
    defaultValues: {
      definition: JSON.stringify({
        nodes: [
          { id: '1', type: 'trigger', position: { x: 0, y: 0 }, config: {} },
          { id: '2', type: 'agent', position: { x: 200, y: 0 }, config: {} }
        ],
        edges: [
          { source: '1', target: '2' }
        ]
      }, null, 2),
    },
  })

  const onSubmit = async (data: CreateWorkflowForm) => {
    setIsLoading(true)
    try {
      const payload = {
        ...data,
        definition: JSON.parse(data.definition),
      }
      await api.post('/workflows', payload)
      toast.success('Workflow created successfully!')
      reset()
      onSuccess()
    } catch (error) {
      toast.error('Failed to create workflow')
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Create New Workflow</DialogTitle>
          <DialogDescription>
            Set up a new workflow to orchestrate AI agents and tools.
          </DialogDescription>
        </DialogHeader>

        <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="name">Name</Label>
            <Input
              id="name"
              placeholder="Customer Onboarding Workflow"
              {...register('name')}
              className={errors.name ? 'border-red-500' : ''}
            />
            {errors.name && (
              <p className="text-sm text-red-500">{errors.name.message}</p>
            )}
          </div>

          <div className="space-y-2">
            <Label htmlFor="description">Description</Label>
            <Input
              id="description"
              placeholder="Brief description of the workflow's purpose"
              {...register('description')}
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="definition">Workflow Definition (JSON)</Label>
            <Textarea
              id="definition"
              placeholder="Workflow structure and configuration..."
              rows={12}
              {...register('definition')}
              className={`font-mono text-sm ${errors.definition ? 'border-red-500' : ''}`}
            />
            {errors.definition && (
              <p className="text-sm text-red-500">{errors.definition.message}</p>
            )}
            <p className="text-xs text-muted-foreground">
              Define nodes (agents, tools, conditions) and their connections in JSON format.
            </p>
          </div>

          <div className="flex justify-end gap-2 pt-4">
            <Button type="button" variant="outline" onClick={() => onOpenChange(false)}>
              Cancel
            </Button>
            <Button type="submit" disabled={isLoading}>
              {isLoading ? 'Creating...' : 'Create Workflow'}
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  )
}