import { IsString, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, IsOptional } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';
import { AgentType, AIProvider } from '@synapseai/shared';

export class CreateAgentDto {
  @ApiProperty()
  @IsString()
  name: string;

  @ApiProperty()
  @IsString()
  description: string;

  @ApiProperty({ enum: AgentType })
  @IsEnum(AgentType)
  type: AgentType;

  @ApiProperty({ enum: AIProvider })
  @IsEnum(AIProvider)
  aiProvider: AIProvider;

  @ApiProperty()
  @IsString()
  model: string;

  @ApiProperty()
  @IsString()
  systemPrompt: string;

  @ApiProperty({ required: false })
  @IsArray()
  @IsOptional()
  tools?: string[];
}

export class UpdateAgentDto {
  @ApiProperty({ required: false })
  @IsString()
  @IsOptional()
  name?: string;

  @ApiProperty({ required: false })
  @IsString()
  @IsOptional()
  description?: string;

  @ApiProperty({ enum: AgentType, required: false })
  @IsEnum(AgentType)
  @IsOptional()
  type?: AgentType;

  @ApiProperty({ enum: AIProvider, required: false })
  @IsEnum(AIProvider)
  @IsOptional()
  aiProvider?: AIProvider;

  @ApiProperty({ required: false })
  @IsString()
  @IsOptional()
  model?: string;

  @ApiProperty({ required: false })
  @IsString()
  @IsOptional()
  systemPrompt?: string;

  @ApiProperty({ required: false })
  @IsArray()
  @IsOptional()
  tools?: string[];
}

export class ExecuteAgentDto {
  @ApiProperty()
  @IsString()
  input: string;

  @ApiProperty({ required: false })
  @IsOptional()
  context?: Record<string, any>;
}