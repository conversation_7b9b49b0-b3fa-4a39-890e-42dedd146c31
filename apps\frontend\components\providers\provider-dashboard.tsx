'use client'

import { useQuery } from 'react-query'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Progress } from '@/components/ui/progress'
import { api } from '@/lib/api'
import { RefreshCw, Zap, Clock, AlertTriangle, CheckCircle, XCircle, TrendingUp } from 'lucide-react'
import { formatDistanceToNow } from 'date-fns'

export function ProviderDashboard() {
  const { data: providers, isLoading: providersLoading, refetch: refetchProviders } = useQuery(
    'providers',
    () => api.get('/providers').then(res => res.data),
    { refetchInterval: 30000 } // Refresh every 30 seconds
  )

  const { data: health, isLoading: healthLoading, refetch: refetchHealth } = useQuery(
    'provider-health',
    () => api.get('/providers/health').then(res => res.data),
    { refetchInterval: 10000 } // Refresh every 10 seconds
  )

  const { data: usage, isLoading: usageLoading } = useQuery(
    'provider-usage',
    () => api.get('/providers/usage').then(res => res.data)
  )

  const getHealthIcon = (isHealthy: boolean) => {
    return isHealthy ? (
      <CheckCircle className="h-4 w-4 text-green-500" />
    ) : (
      <XCircle className="h-4 w-4 text-red-500" />
    )
  }

  const getLatencyColor = (latency: number) => {
    if (latency < 1000) return 'text-green-600'
    if (latency < 3000) return 'text-yellow-600'
    return 'text-red-600'
  }

  const handleRefresh = () => {
    refetchProviders()
    refetchHealth()
  }

  if (providersLoading || healthLoading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <h2 className="text-2xl font-bold">Provider Dashboard</h2>
          <Button disabled>
            <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
            Loading...
          </Button>
        </div>
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
          {Array.from({ length: 6 }).map((_, i) => (
            <Card key={i} className="animate-pulse">
              <CardHeader>
                <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                <div className="h-3 bg-gray-200 rounded w-1/2"></div>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  <div className="h-3 bg-gray-200 rounded"></div>
                  <div className="h-3 bg-gray-200 rounded w-2/3"></div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">Provider Dashboard</h2>
          <p className="text-muted-foreground">
            Monitor AI provider status, performance, and usage
          </p>
        </div>
        <Button onClick={handleRefresh}>
          <RefreshCw className="h-4 w-4 mr-2" />
          Refresh
        </Button>
      </div>

      {/* Provider Status Grid */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
        {providers?.map((provider: any) => {
          const healthInfo = health?.find((h: any) => h.provider === provider.id)
          const usageInfo = usage?.find((u: any) => u.provider === provider.id)

          return (
            <Card key={provider.id} className="hover:shadow-md transition-shadow">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <div className="flex items-center space-x-2">
                  <CardTitle className="text-lg">{provider.name}</CardTitle>
                  {getHealthIcon(healthInfo?.isHealthy ?? false)}
                </div>
                <Badge variant={provider.isConfigured ? 'default' : 'destructive'}>
                  {provider.isConfigured ? 'Configured' : 'Not Configured'}
                </Badge>
              </CardHeader>
              <CardContent>
                <CardDescription className="mb-4">
                  {provider.description}
                </CardDescription>

                {/* Health Metrics */}
                {healthInfo && (
                  <div className="space-y-3 mb-4">
                    <div className="flex items-center justify-between text-sm">
                      <span className="flex items-center gap-1">
                        <Clock className="h-3 w-3" />
                        Latency
                      </span>
                      <span className={`font-medium ${getLatencyColor(healthInfo.latency)}`}>
                        {healthInfo.latency}ms
                      </span>
                    </div>
                    
                    <div className="flex items-center justify-between text-sm">
                      <span>Error Rate</span>
                      <span className="font-medium">
                        {(healthInfo.errorRate * 100).toFixed(1)}%
                      </span>
                    </div>

                    {healthInfo.rateLimitStatus && (
                      <div className="space-y-1">
                        <div className="flex items-center justify-between text-sm">
                          <span>Rate Limit</span>
                          <span className="font-medium">
                            {healthInfo.rateLimitStatus.remaining} remaining
                          </span>
                        </div>
                        {healthInfo.rateLimitStatus.isLimited && (
                          <div className="text-xs text-red-600">
                            Resets {formatDistanceToNow(new Date(healthInfo.rateLimitStatus.resetTime), { addSuffix: true })}
                          </div>
                        )}
                      </div>
                    )}

                    <div className="text-xs text-muted-foreground">
                      Last checked: {formatDistanceToNow(new Date(healthInfo.lastChecked), { addSuffix: true })}
                    </div>
                  </div>
                )}

                {/* Usage Stats */}
                {usageInfo && (
                  <div className="space-y-2 pt-2 border-t">
                    <div className="flex items-center gap-1 text-sm font-medium">
                      <TrendingUp className="h-3 w-3" />
                      Usage (30 days)
                    </div>
                    <div className="space-y-1">
                      <div className="flex justify-between text-xs">
                        <span>Requests</span>
                        <span className="font-medium">{usageInfo.requests?.toLocaleString()}</span>
                      </div>
                      <div className="flex justify-between text-xs">
                        <span>Tokens</span>
                        <span className="font-medium">{usageInfo.totalTokens?.toLocaleString()}</span>
                      </div>
                      <div className="flex justify-between text-xs">
                        <span>Cost</span>
                        <span className="font-medium">${usageInfo.cost?.toFixed(2)}</span>
                      </div>
                    </div>
                  </div>
                )}

                {/* Capabilities */}
                <div className="space-y-2 pt-2 border-t">
                  <div className="text-sm font-medium">Capabilities</div>
                  <div className="flex flex-wrap gap-1">
                    {provider.capabilities?.map((capability: string) => (
                      <Badge key={capability} variant="outline" className="text-xs">
                        {capability.replace('_', ' ')}
                      </Badge>
                    ))}
                  </div>
                </div>

                {/* Models */}
                <div className="space-y-2 pt-2 border-t">
                  <div className="text-sm font-medium">
                    Models ({provider.models?.length || 0})
                  </div>
                  <div className="max-h-16 overflow-y-auto">
                    <div className="space-y-1">
                      {provider.models?.slice(0, 3).map((model: string) => (
                        <div key={model} className="text-xs text-muted-foreground">
                          {model}
                        </div>
                      ))}
                      {provider.models?.length > 3 && (
                        <div className="text-xs text-muted-foreground">
                          +{provider.models.length - 3} more...
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          )
        })}
      </div>

      {/* Overall Statistics */}
      {usage && usage.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle>Usage Summary (Last 30 Days)</CardTitle>
            <CardDescription>
              Aggregated usage across all providers
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid gap-4 md:grid-cols-4">
              <div className="space-y-2">
                <div className="text-2xl font-bold">
                  {usage.reduce((sum: number, p: any) => sum + (p.requests || 0), 0).toLocaleString()}
                </div>
                <div className="text-sm text-muted-foreground">Total Requests</div>
              </div>
              <div className="space-y-2">
                <div className="text-2xl font-bold">
                  {usage.reduce((sum: number, p: any) => sum + (p.totalTokens || 0), 0).toLocaleString()}
                </div>
                <div className="text-sm text-muted-foreground">Total Tokens</div>
              </div>
              <div className="space-y-2">
                <div className="text-2xl font-bold">
                  ${usage.reduce((sum: number, p: any) => sum + (p.cost || 0), 0).toFixed(2)}
                </div>
                <div className="text-sm text-muted-foreground">Total Cost</div>
              </div>
              <div className="space-y-2">
                <div className="text-2xl font-bold">
                  {providers?.filter((p: any) => p.isConfigured && p.health?.isHealthy).length || 0}
                </div>
                <div className="text-sm text-muted-foreground">Healthy Providers</div>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Provider Health Alert */}
      {health?.some((h: any) => !h.isHealthy) && (
        <Card className="border-yellow-200 bg-yellow-50">
          <CardHeader>
            <CardTitle className="flex items-center gap-2 text-yellow-800">
              <AlertTriangle className="h-5 w-5" />
              Provider Health Alert
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              {health
                .filter((h: any) => !h.isHealthy)
                .map((h: any) => (
                  <div key={h.provider} className="flex items-center justify-between">
                    <span className="font-medium text-yellow-800">
                      {providers?.find((p: any) => p.id === h.provider)?.name || h.provider}
                    </span>
                    <span className="text-sm text-yellow-600">
                      Error rate: {(h.errorRate * 100).toFixed(1)}%
                    </span>
                  </div>
                ))}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  )
}