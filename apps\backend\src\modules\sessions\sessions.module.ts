import { Module } from '@nestjs/common';
import { SessionsService } from './sessions.service';
import { SessionsController } from './sessions.controller';
import { PrismaModule } from '../../common/prisma/prisma.module';
import { ApixModule } from '../apix/apix.module';

@Module({
  imports: [PrismaModule, ApixModule],
  controllers: [SessionsController],
  providers: [SessionsService],
  exports: [SessionsService],
})
export class SessionsModule {}