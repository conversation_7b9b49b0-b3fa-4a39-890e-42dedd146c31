import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Agent } from './entities/agent.entity';
import { AgentExecution } from './entities/agent-execution.entity';
import { CreateAgentDto, UpdateAgentDto, ExecuteAgentDto } from './dto/agent.dto';
import { AgentStatus, ExecutionStatus } from '@synapseai/shared';

@Injectable()
export class AgentsService {
  constructor(
    @InjectRepository(Agent)
    private agentRepository: Repository<Agent>,
    @InjectRepository(AgentExecution)
    private executionRepository: Repository<AgentExecution>,
  ) {}

  async create(createAgentDto: CreateAgentDto, organizationId: string, createdBy: string) {
    const agent = this.agentRepository.create({
      ...createAgentDto,
      organizationId,
      createdBy,
      status: AgentStatus.IDLE,
      memory: { shortTerm: {}, longTerm: {}, context: [] },
    });
    return this.agentRepository.save(agent);
  }

  async findAll(organizationId: string) {
    return this.agentRepository.find({
      where: { organizationId },
      order: { createdAt: 'DESC' },
    });
  }

  async findOne(id: string, organizationId: string) {
    const agent = await this.agentRepository.findOne({
      where: { id, organizationId },
    });
    if (!agent) {
      throw new NotFoundException('Agent not found');
    }
    return agent;
  }

  async update(id: string, updateAgentDto: UpdateAgentDto, organizationId: string) {
    const agent = await this.findOne(id, organizationId);
    Object.assign(agent, updateAgentDto);
    return this.agentRepository.save(agent);
  }

  async remove(id: string, organizationId: string) {
    const agent = await this.findOne(id, organizationId);
    await this.agentRepository.remove(agent);
    return { message: 'Agent deleted successfully' };
  }

  async execute(id: string, executeAgentDto: ExecuteAgentDto, organizationId: string) {
    const agent = await this.findOne(id, organizationId);
    
    const execution = this.executionRepository.create({
      agentId: id,
      input: executeAgentDto.input,
      status: ExecutionStatus.PENDING,
      steps: [],
    });
    
    const savedExecution = await this.executionRepository.save(execution);
    
    // TODO: Implement actual AI execution logic
    // This is a placeholder for the AI processing
    setTimeout(async () => {
      savedExecution.status = ExecutionStatus.COMPLETED;
      savedExecution.output = `Agent ${agent.name} processed: ${executeAgentDto.input}`;
      savedExecution.completedAt = new Date();
      await this.executionRepository.save(savedExecution);
    }, 2000);
    
    return savedExecution;
  }
}