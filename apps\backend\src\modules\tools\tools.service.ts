import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Tool } from './entities/tool.entity';
import { CreateToolDto, UpdateToolDto } from './dto/tool.dto';
import { ToolStatus, ToolCategory } from '@synapseai/shared';

@Injectable()
export class ToolsService {
  constructor(
    @InjectRepository(Tool)
    private toolRepository: Repository<Tool>,
  ) {}

  async create(createToolDto: CreateToolDto, organizationId: string) {
    const tool = this.toolRepository.create({
      ...createToolDto,
      organizationId,
      status: ToolStatus.ACTIVE,
      isPublic: false,
    });
    return this.toolRepository.save(tool);
  }

  async findAll(organizationId: string) {
    return this.toolRepository.find({
      where: [
        { organizationId },
        { isPublic: true },
      ],
      order: { createdAt: 'DESC' },
    });
  }

  async getMarketplace() {
    // Return pre-built tools from marketplace
    const marketplaceTools = [
      {
        id: 'slack-integration',
        name: 'Slack Integration',
        description: 'Send messages and notifications to Slack channels',
        category: ToolCategory.COMMUNICATION,
        version: '2.1.0',
        isPublic: true,
        rating: 4.8,
        installs: 12547,
      },
      {
        id: 'salesforce-crm',
        name: 'Salesforce CRM',
        description: 'Manage leads, contacts, and opportunities',
        category: ToolCategory.CRM,
        version: '3.2.1',
        isPublic: true,
        rating: 4.6,
        installs: 8921,
      },
      {
        id: 'google-analytics',
        name: 'Google Analytics',
        description: 'Fetch analytics data and generate reports',
        category: ToolCategory.ANALYTICS,
        version: '1.8.4',
        isPublic: true,
        rating: 4.7,
        installs: 15632,
      },
    ];

    return marketplaceTools;
  }

  async findOne(id: string, organizationId: string) {
    const tool = await this.toolRepository.findOne({
      where: [
        { id, organizationId },
        { id, isPublic: true },
      ],
    });
    if (!tool) {
      throw new NotFoundException('Tool not found');
    }
    return tool;
  }

  async update(id: string, updateToolDto: UpdateToolDto, organizationId: string) {
    const tool = await this.findOne(id, organizationId);
    Object.assign(tool, updateToolDto);
    return this.toolRepository.save(tool);
  }

  async remove(id: string, organizationId: string) {
    const tool = await this.findOne(id, organizationId);
    await this.toolRepository.remove(tool);
    return { message: 'Tool deleted successfully' };
  }
}