import { memo } from 'react'
import { Handle, Position, NodeProps } from 'reactflow'
import { Card, CardContent } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { CheckCircle } from 'lucide-react'

export const OutputNode = memo(({ data, selected }: NodeProps) => {
  return (
    <Card className={`min-w-[200px] ${selected ? 'ring-2 ring-primary' : ''} border-purple-200`}>
      <CardContent className="p-3">
        <div className="flex items-center gap-2 mb-2">
          <CheckCircle className="h-4 w-4 text-purple-500" />
          <span className="font-medium text-sm">{data.label || 'Output'}</span>
        </div>
        
        <Badge variant="default" className="text-xs bg-purple-500">
          End Point
        </Badge>
        
        <p className="text-xs text-muted-foreground mt-2">
          Format: {data.format || 'JSON'}
        </p>
        
        {data.template && (
          <p className="text-xs text-muted-foreground font-mono mt-1">
            {data.template.substring(0, 20)}...
          </p>
        )}
      </CardContent>
      
      <Handle type="target" position={Position.Left} className="w-3 h-3" />
    </Card>
  )
})