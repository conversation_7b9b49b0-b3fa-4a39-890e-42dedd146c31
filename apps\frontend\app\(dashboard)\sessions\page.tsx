'use client'

import { useQuery } from 'react-query'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { LoadingSpinner } from '@/components/ui/loading-spinner'
import { api } from '@/lib/api'
import { MessageCircle, Calendar, User, Bot, Trash2 } from 'lucide-react'
import { formatDistanceToNow } from 'date-fns'
import { useMutation, useQueryClient } from 'react-query'
import toast from 'react-hot-toast'

export default function SessionsPage() {
  const queryClient = useQueryClient()
  
  const { data: sessions, isLoading } = useQuery('sessions', () =>
    api.get('/sessions').then(res => res.data)
  )

  const deleteMutation = useMutation(
    (sessionId: string) => api.delete(`/sessions/${sessionId}`),
    {
      onSuccess: () => {
        toast.success('Session deleted successfully!')
        queryClient.invalidateQueries('sessions')
      },
      onError: () => {
        toast.error('Failed to delete session')
      },
    }
  )

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <LoadingSpinner />
      </div>
    )
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Sessions</h1>
          <p className="text-muted-foreground">
            View and manage conversation sessions
          </p>
        </div>
      </div>

      {sessions?.length === 0 ? (
        <div className="text-center py-12">
          <MessageCircle className="h-12 w-12 mx-auto mb-4 text-muted-foreground" />
          <h3 className="text-lg font-semibold mb-2">No sessions yet</h3>
          <p className="text-muted-foreground">
            Start chatting with agents to create sessions
          </p>
        </div>
      ) : (
        <div className="grid gap-4">
          {sessions?.map((session: any) => (
            <Card key={session.id} className="hover:shadow-md transition-shadow">
              <CardHeader>
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <MessageCircle className="h-5 w-5 text-primary" />
                    <div>
                      <CardTitle className="text-lg">
                        {session.name || `Session ${session.id.slice(-8)}`}
                      </CardTitle>
                      <CardDescription className="flex items-center gap-4 mt-1">
                        {session.agent && (
                          <span className="flex items-center gap-1">
                            <Bot className="h-3 w-3" />
                            {session.agent.name}
                          </span>
                        )}
                        {session.user && (
                          <span className="flex items-center gap-1">
                            <User className="h-3 w-3" />
                            {session.user.firstName} {session.user.lastName}
                          </span>
                        )}
                        <span className="flex items-center gap-1">
                          <Calendar className="h-3 w-3" />
                          {formatDistanceToNow(new Date(session.createdAt), { addSuffix: true })}
                        </span>
                      </CardDescription>
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    <Badge variant={session.status === 'ACTIVE' ? 'default' : 'secondary'}>
                      {session.status}
                    </Badge>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => deleteMutation.mutate(session.id)}
                      disabled={deleteMutation.isLoading}
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex items-center justify-between text-sm">
                    <span className="text-muted-foreground">Messages:</span>
                    <span className="font-medium">{session.messages?.length || 0}</span>
                  </div>
                  
                  {session.messages && session.messages.length > 0 && (
                    <div className="space-y-2">
                      <h4 className="text-sm font-medium">Recent Messages:</h4>
                      <div className="space-y-1">
                        {session.messages.slice(-3).map((message: any) => (
                          <div key={message.id} className="text-sm p-2 bg-muted rounded">
                            <div className="flex items-center justify-between mb-1">
                              <Badge variant="outline" className="text-xs">
                                {message.role}
                              </Badge>
                              <span className="text-xs text-muted-foreground">
                                {formatDistanceToNow(new Date(message.createdAt), { addSuffix: true })}
                              </span>
                            </div>
                            <p className="text-xs text-muted-foreground line-clamp-2">
                              {message.content}
                            </p>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}
    </div>
  )
}