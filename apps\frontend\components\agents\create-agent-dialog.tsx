'use client'

import { useState } from 'react'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import { useQuery } from 'react-query'
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { api } from '@/lib/api'
import toast from 'react-hot-toast'

const createAgentSchema = z.object({
  name: z.string().min(1, 'Name is required'),
  description: z.string().optional(),
  type: z.enum(['STANDALONE', 'TOOL_DRIVEN', 'HYBRID', 'MULTI_TASK', 'MULTI_PROVIDER']),
  systemPrompt: z.string().min(1, 'System prompt is required'),
  provider: z.string().min(1, 'Provider is required'),
  model: z.string().min(1, 'Model is required'),
  temperature: z.number().min(0).max(2),
  maxTokens: z.number().min(1).max(8192),
})

type CreateAgentForm = z.infer<typeof createAgentSchema>

interface CreateAgentDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  onSuccess: () => void
}

export function CreateAgentDialog({ open, onOpenChange, onSuccess }: CreateAgentDialogProps) {
  const [isLoading, setIsLoading] = useState(false)

  const { data: providers } = useQuery('providers', () =>
    api.get('/providers').then(res => res.data)
  )

  const {
    register,
    handleSubmit,
    formState: { errors },
    setValue,
    watch,
    reset,
  } = useForm<CreateAgentForm>({
    resolver: zodResolver(createAgentSchema),
    defaultValues: {
      type: 'STANDALONE',
      temperature: 0.7,
      maxTokens: 2048,
      systemPrompt: 'You are a helpful AI assistant.',
    },
  })

  const selectedProvider = watch('provider')

  const onSubmit = async (data: CreateAgentForm) => {
    setIsLoading(true)
    try {
      await api.post('/agents', data)
      toast.success('Agent created successfully!')
      reset()
      onSuccess()
    } catch (error) {
      toast.error('Failed to create agent')
    } finally {
      setIsLoading(false)
    }
  }

  const getModelsForProvider = (providerId: string) => {
    const provider = providers?.find((p: any) => p.id === providerId)
    return provider?.models || []
  }

  const getHealthyProviders = () => {
    return providers?.filter((p: any) => p.isConfigured && p.health?.isHealthy) || []
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Create New Agent</DialogTitle>
          <DialogDescription>
            Set up a new AI agent with specific capabilities and configuration.
          </DialogDescription>
        </DialogHeader>

        <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="name">Name</Label>
              <Input
                id="name"
                placeholder="Customer Support Agent"
                {...register('name')}
                className={errors.name ? 'border-red-500' : ''}
              />
              {errors.name && (
                <p className="text-sm text-red-500">{errors.name.message}</p>
              )}
            </div>
            <div className="space-y-2">
              <Label htmlFor="type">Type</Label>
              <Select onValueChange={(value) => setValue('type', value as any)}>
                <SelectTrigger>
                  <SelectValue placeholder="Select agent type" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="STANDALONE">Standalone</SelectItem>
                  <SelectItem value="TOOL_DRIVEN">Tool Driven</SelectItem>
                  <SelectItem value="HYBRID">Hybrid</SelectItem>
                  <SelectItem value="MULTI_TASK">Multi Task</SelectItem>
                  <SelectItem value="MULTI_PROVIDER">Multi Provider</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="description">Description</Label>
            <Input
              id="description"
              placeholder="Brief description of the agent's purpose"
              {...register('description')}
            />
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="provider">Provider</Label>
              <Select onValueChange={(value) => setValue('provider', value)}>
                <SelectTrigger>
                  <SelectValue placeholder="Select provider" />
                </SelectTrigger>
                <SelectContent>
                  {getHealthyProviders().map((provider: any) => (
                    <SelectItem key={provider.id} value={provider.id}>
                      <div className="flex items-center gap-2">
                        <span>{provider.name}</span>
                        {provider.health?.isHealthy && (
                          <span className="w-2 h-2 bg-green-500 rounded-full"></span>
                        )}
                        {provider.health?.latency && (
                          <span className="text-xs text-muted-foreground">
                            {provider.health.latency}ms
                          </span>
                        )}
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              {errors.provider && (
                <p className="text-sm text-red-500">{errors.provider.message}</p>
              )}
              {providers && providers.filter((p: any) => p.isConfigured).length === 0 && (
                <p className="text-sm text-amber-600">
                  No providers configured. Check your API keys in settings.
                </p>
              )}
            </div>
            <div className="space-y-2">
              <Label htmlFor="model">Model</Label>
              <Select onValueChange={(value) => setValue('model', value)} disabled={!selectedProvider}>
                <SelectTrigger>
                  <SelectValue placeholder="Select model" />
                </SelectTrigger>
                <SelectContent>
                  {getModelsForProvider(selectedProvider)?.map((model: string) => (
                    <SelectItem key={model} value={model}>
                      {model}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              {errors.model && (
                <p className="text-sm text-red-500">{errors.model.message}</p>
              )}
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="systemPrompt">System Prompt</Label>
            <Textarea
              id="systemPrompt"
              placeholder="Define the agent's role and behavior..."
              rows={4}
              {...register('systemPrompt')}
              className={errors.systemPrompt ? 'border-red-500' : ''}
            />
            {errors.systemPrompt && (
              <p className="text-sm text-red-500">{errors.systemPrompt.message}</p>
            )}
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="temperature">Temperature</Label>
              <Input
                id="temperature"
                type="number"
                step="0.1"
                min="0"
                max="2"
                {...register('temperature', { valueAsNumber: true })}
                className={errors.temperature ? 'border-red-500' : ''}
              />
              <p className="text-xs text-muted-foreground">
                0 = deterministic, 2 = very creative
              </p>
              {errors.temperature && (
                <p className="text-sm text-red-500">{errors.temperature.message}</p>
              )}
            </div>
            <div className="space-y-2">
              <Label htmlFor="maxTokens">Max Tokens</Label>
              <Input
                id="maxTokens"
                type="number"
                min="1"
                max="8192"
                {...register('maxTokens', { valueAsNumber: true })}
                className={errors.maxTokens ? 'border-red-500' : ''}
              />
              <p className="text-xs text-muted-foreground">
                Maximum response length
              </p>
              {errors.maxTokens && (
                <p className="text-sm text-red-500">{errors.maxTokens.message}</p>
              )}
            </div>
          </div>

          <div className="flex justify-end gap-2 pt-4">
            <Button type="button" variant="outline" onClick={() => onOpenChange(false)}>
              Cancel
            </Button>
            <Button type="submit" disabled={isLoading}>
              {isLoading ? 'Creating...' : 'Create Agent'}
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  )
}