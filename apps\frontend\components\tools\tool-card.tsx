import { Card, CardContent, CardDescription, Card<PERSON>eader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Wrench, Play, Settings, Activity } from 'lucide-react'
import { useState } from 'react'
import { useMutation } from 'react-query'
import { api } from '@/lib/api'
import toast from 'react-hot-toast'

interface ToolCardProps {
  tool: any
  onUpdate: () => void
}

export function ToolCard({ tool, onUpdate }: ToolCardProps) {
  const [showExecuteDialog, setShowExecuteDialog] = useState(false)

  const executeMutation = useMutation(
    (input: any) => api.post(`/tools/${tool.id}/execute`, { input }),
    {
      onSuccess: () => {
        toast.success('Tool executed successfully!')
        onUpdate()
      },
      onError: () => {
        toast.error('Failed to execute tool')
      },
    }
  )

  const getToolTypeColor = (type: string) => {
    switch (type) {
      case 'FUNCTION_CALL': return 'bg-blue-100 text-blue-800'
      case 'REST_API': return 'bg-green-100 text-green-800'
      case 'RAG_RETRIEVAL': return 'bg-purple-100 text-purple-800'
      case 'BROWSER_ACTION': return 'bg-orange-100 text-orange-800'
      case 'DATABASE_QUERY': return 'bg-red-100 text-red-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  const handleQuickExecute = () => {
    // Simple execution for demo - in production, you'd want a proper form
    const demoInput = tool.type === 'FUNCTION_CALL' 
      ? { operation: 'add', a: 5, b: 3 }
      : tool.type === 'RAG_RETRIEVAL'
      ? { query: 'test query' }
      : {}
    
    executeMutation.mutate(demoInput)
  }

  return (
    <Card className="hover:shadow-md transition-shadow">
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <div className="flex items-center space-x-2">
          <Wrench className="h-5 w-5 text-primary" />
          <CardTitle className="text-lg">{tool.name}</CardTitle>
        </div>
        <Badge variant={tool.isActive ? 'default' : 'secondary'}>
          {tool.isActive ? 'Active' : 'Inactive'}
        </Badge>
      </CardHeader>
      <CardContent>
        <CardDescription className="mb-4">
          {tool.description || 'No description provided'}
        </CardDescription>
        
        <div className="space-y-2 mb-4">
          <div className="flex justify-between items-center">
            <span className="text-sm text-muted-foreground">Type:</span>
            <Badge className={getToolTypeColor(tool.type)} variant="outline">
              {tool.type.replace('_', ' ')}
            </Badge>
          </div>
          
          {tool.executions && (
            <div className="flex justify-between text-sm">
              <span className="text-muted-foreground">Recent Executions:</span>
              <span className="font-medium">{tool.executions.length}</span>
            </div>
          )}
          
          {tool.agentConnections && (
            <div className="flex justify-between text-sm">
              <span className="text-muted-foreground">Connected Agents:</span>
              <span className="font-medium">{tool.agentConnections.length}</span>
            </div>
          )}
        </div>

        <div className="flex gap-2">
          <Button 
            size="sm" 
            className="flex-1"
            onClick={handleQuickExecute}
            disabled={executeMutation.isLoading}
          >
            <Play className="h-4 w-4 mr-2" />
            {executeMutation.isLoading ? 'Running...' : 'Test Run'}
          </Button>
          <Button variant="outline" size="sm">
            <Settings className="h-4 w-4" />
          </Button>
        </div>

        {tool.executions && tool.executions.length > 0 && (
          <div className="mt-4 pt-4 border-t">
            <div className="flex items-center gap-2 mb-2">
              <Activity className="h-4 w-4 text-muted-foreground" />
              <span className="text-sm font-medium">Recent Activity</span>
            </div>
            <div className="space-y-1">
              {tool.executions.slice(0, 3).map((execution: any) => (
                <div key={execution.id} className="flex justify-between items-center text-xs">
                  <span className="text-muted-foreground">
                    {new Date(execution.createdAt).toLocaleDateString()}
                  </span>
                  <Badge 
                    variant={execution.status === 'COMPLETED' ? 'default' : 
                            execution.status === 'FAILED' ? 'destructive' : 'secondary'}
                    className="text-xs"
                  >
                    {execution.status}
                  </Badge>
                </div>
              ))}
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  )
}